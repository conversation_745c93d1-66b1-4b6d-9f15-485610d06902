APP_ENV=dev
LOG_LEVEL=trace
LOG_DAILY=true
LOG_PATH=logs
MAX_SIZE=100
MAX_AGE=5
MAX_BACKUPS=5
COMPRESS=true

FIBER_PORT=3000

REDIS_PORT=6326
REDIS_DB=0
REDIS_HOST=127.0.0.1:${REDIS_PORT}
REDIS_PASSWORD=123456Xyz@
REDIS_USERNAME=googledsp
REDIS_URI=redis://${REDIS_USERNAME}:${REDIS_PASSWORD}@${REDIS_HOST}
REDIS_CACHE=60

DOMAIN=http://localhost:${FIBER_PORT}
UPLOAD_PATH_EDITORS=../editors/
PATH_EDITORS=/static/editors
UPLOAD_PATH_PUBLIC=public
DEFAULT_IMG_ADMIN=/user-dummy-img.jpg

MONGODB_HOST=localhost
MONGODB_PORT=27018
MONGODB_USER=googledsp
MONGODB_PASS=123456Xyz
MONGODB_DATABASE=googledsp
MONGODB_DSN=mongodb://${MONGODB_USER}:${MONGODB_PASS}@${MONGODB_HOST}:${MONGODB_PORT}/${MONGODB_DATABASE}

POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=googledsp
POSTGRES_PASSWORD=123456Xyz
POSTGRES_DATABASE=googledsp
POSTGRES_DSN=postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DATABASE}?sslmode=disable

JWT_SECRET=your-secret-key-here
JWT_EXPIRE=24h

TELEGRAM_TOKEN=
TELEGRAM_GROUP_DEV=
TELEGRAM_DEV=true

# Google DSP API Configuration
GOOGLE_DSP_CLIENT_ID=
GOOGLE_DSP_CLIENT_SECRET=
GOOGLE_DSP_REFRESH_TOKEN=
GOOGLE_DSP_DEVELOPER_TOKEN=

# Google Ads API Configuration
GOOGLE_ADS_CLIENT_ID=
GOOGLE_ADS_CLIENT_SECRET=
GOOGLE_ADS_REFRESH_TOKEN=
GOOGLE_ADS_DEVELOPER_TOKEN=
GOOGLE_ADS_CUSTOMER_ID=

# Campaign Management
DEFAULT_CAMPAIGN_BUDGET=1000
DEFAULT_BID_STRATEGY=TARGET_CPA
DEFAULT_TARGET_CPA=10.00

# Reporting
REPORT_TIMEZONE=Asia/Ho_Chi_Minh
REPORT_DATE_FORMAT=2006-01-02
