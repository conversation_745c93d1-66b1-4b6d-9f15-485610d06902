#!/bin/bash

# Test script for AdGroups and Ads API endpoints
echo "Testing AdGroups and Ads API endpoints..."

# Test AdGroups API
echo "Testing AdGroups API: /dsp/googleads/api/adgroups/list-table"
curl -X POST http://localhost:8080/dsp/googleads/api/adgroups/list-table \
  -H "Content-Type: application/json" \
  -d '{
    "draw": 1,
    "start": 0,
    "length": 10,
    "search": null,
    "order": [{"column": 0, "dir": "asc"}]
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo -e "\n" + "="*50 + "\n"

# Test Ads API
echo "Testing Ads API: /dsp/googleads/api/ads/list-table"
curl -X POST http://localhost:8080/dsp/googleads/api/ads/list-table \
  -H "Content-Type: application/json" \
  -d '{
    "draw": 1,
    "start": 0,
    "length": 10,
    "search": null,
    "order": [{"column": 0, "dir": "asc"}]
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo -e "\n" + "="*50 + "\n"

# Test with search parameter
echo "Testing AdGroups API with search: 'Electronics'"
curl -X POST http://localhost:8080/dsp/googleads/api/adgroups/list-table \
  -H "Content-Type: application/json" \
  -d '{
    "draw": 2,
    "start": 0,
    "length": 5,
    "search": "Electronics",
    "order": [{"column": 0, "dir": "asc"}]
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo -e "\n" + "="*50 + "\n"

# Test with search parameter for Ads
echo "Testing Ads API with search: 'Video'"
curl -X POST http://localhost:8080/dsp/googleads/api/ads/list-table \
  -H "Content-Type: application/json" \
  -d '{
    "draw": 2,
    "start": 0,
    "length": 5,
    "search": "Video",
    "order": [{"column": 0, "dir": "asc"}]
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo -e "\nAPI endpoint tests completed!"
