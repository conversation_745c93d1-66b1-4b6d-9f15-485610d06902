package conf

import (
	"os"

	"github.com/joho/godotenv"
	"github.com/namsral/flag"
)

const (
	KeyCompReportMongoDB = "report_mongodb"
)

var (
	UploadPathPublic  = "public"
	UploadPathEditors = "../editors/"
)

func init() {
	if err := ReadFileEnv(); err != nil {
		panic("read file env")
	}

	flag.StringVar(&UploadPathPublic, "upload-path-public", "public", "upload public public default: public")
	flag.StringVar(&UploadPathEditors, "upload-path-editors", "../editors/", "upload editors default")

	flag.Parse()
}

func ReadFileEnv() error {
	envFile := os.Getenv("ENV_FILE")
	if envFile == "" {
		envFile = ".env"
	}
	_, err := os.Stat(envFile)
	if err == nil {
		err := godotenv.Load(envFile)
		if err != nil {
			return err
		}
	}
	return nil
}
