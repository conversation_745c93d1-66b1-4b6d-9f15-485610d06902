# Google Ads API Endpoints Documentation

This document describes the newly created API endpoints for AdGroups and Ads, following the same pattern as the existing Campaigns API.

## 📋 Overview

Three DataTable API endpoints are now available:
- **Campaigns**: `/dsp/googleads/api/campaigns/list-table` (existing)
- **AdGroups**: `/dsp/googleads/api/adgroups/list-table` (new)
- **Ads**: `/dsp/googleads/api/ads/list-table` (new)

All endpoints follow the same request/response pattern for consistency.

## 🔗 API Endpoints

### 1. AdGroups API
**Endpoint**: `POST /dsp/googleads/api/adgroups/list-table`

**Request Body**:
```json
{
  "draw": 1,
  "start": 0,
  "length": 10,
  "search": "search term",
  "order": [
    {
      "column": 0,
      "dir": "asc"
    }
  ],
  "adGroupId": "12345678901",
  "campaignId": "98765432109",
  "advertiser_id": "11111111111"
}
```

**Response**:
```json
{
  "draw": 1,
  "data": [
    {
      "DT_RowId": "adgroup_12345678901",
      "adgroup_id": "12345678901",
      "adgroup_name": "Search AdGroup - Electronics",
      "campaign_id": "98765432109",
      "campaign_name": "Summer Sale 2024",
      "advertiser_id": "11111111111",
      "advertiser_name": "TechStore Vietnam",
      "status": "ENABLED",
      "impressions": 125000,
      "clicks": 2000,
      "cost_micro": 2500500000,
      "conversions": 45,
      "average_cpc": 1.25,
      "average_cpm": 15.75,
      "bid_strategy": "TARGET_CPA",
      "target_cpa": 55.56,
      "adgroup_type": "SEARCH_STANDARD"
    }
  ],
  "recordsTotal": 100,
  "recordsFiltered": 100
}
```

### 2. Ads API
**Endpoint**: `POST /dsp/googleads/api/ads/list-table`

**Request Body**:
```json
{
  "draw": 1,
  "start": 0,
  "length": 10,
  "search": "search term",
  "order": [
    {
      "column": 0,
      "dir": "asc"
    }
  ],
  "adId": "12345678901",
  "adGroupId": "98765432101",
  "campaignId": "11111111101",
  "advertiser_id": "22222222201"
}
```

**Response**:
```json
{
  "draw": 1,
  "data": [
    {
      "DT_RowId": "ad_12345678901",
      "ad_id": "12345678901",
      "ad_name": "Responsive Search Ad - Electronics Sale",
      "adgroup_id": "98765432101",
      "adgroup_name": "Search AdGroup - Electronics",
      "campaign_id": "11111111101",
      "campaign_name": "Summer Sale 2024",
      "advertiser_id": "22222222201",
      "advertiser_name": "TechStore Vietnam",
      "status": "ENABLED",
      "impressions": 85000,
      "clicks": 1020,
      "cost_micro": 1250750000,
      "conversions": 28,
      "ad_type": "RESPONSIVE_SEARCH_AD",
      "headline1": "Best Electronics Deals",
      "headline2": "Summer Sale 2024",
      "description1": "Shop the latest electronics with amazing discounts",
      "final_url": "https://techstore.vn/summer-sale-2024",
      "ad_strength": "EXCELLENT"
    }
  ],
  "recordsTotal": 50,
  "recordsFiltered": 50
}
```

## 📊 Request Parameters

### Common Parameters (All Endpoints)
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `draw` | integer | Yes | DataTable draw counter |
| `start` | integer | Yes | Starting record index (0-based) |
| `length` | integer | Yes | Number of records to return (max 100) |
| `search` | string/null | No | Search term for filtering |
| `order` | array | No | Sorting configuration |

### AdGroups Specific Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `adGroupId` | string | No | Filter by specific AdGroup ID |
| `campaignId` | string | No | Filter by Campaign ID |
| `advertiser_id` | string | No | Filter by Advertiser ID |

### Ads Specific Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `adId` | string | No | Filter by specific Ad ID |
| `adGroupId` | string | No | Filter by AdGroup ID |
| `campaignId` | string | No | Filter by Campaign ID |
| `advertiser_id` | string | No | Filter by Advertiser ID |

## 🔍 Sorting Options

### AdGroups Sorting (column index)
- `0`: AdGroup Name
- `1`: Campaign Name  
- `2`: Status
- `3`: Impressions
- `4`: Clicks
- `5`: Cost
- `6`: Conversions

### Ads Sorting (column index)
- `0`: Ad Name
- `1`: AdGroup Name
- `2`: Campaign Name
- `3`: Status
- `4`: Impressions
- `5`: Clicks
- `6`: Cost
- `7`: Conversions

## 📈 Response Data Fields

### AdGroups Response Fields
- **Basic Info**: `adgroup_id`, `adgroup_name`, `campaign_name`, `advertiser_name`, `status`
- **Performance**: `impressions`, `clicks`, `cost_micro`, `conversions`, `average_cpc`, `average_cpm`
- **Configuration**: `bid_strategy`, `target_cpa`, `target_roas`, `adgroup_type`, `rotation_mode`
- **Advanced**: `video_views`, `video_view_rate`, `sk_ad_network_installs`

### Ads Response Fields
- **Basic Info**: `ad_id`, `ad_name`, `adgroup_name`, `campaign_name`, `advertiser_name`, `status`
- **Performance**: `impressions`, `clicks`, `cost_micro`, `conversions`, `average_cpc`, `average_cpm`
- **Ad Content**: `ad_type`, `headline1`, `headline2`, `description1`, `final_url`, `display_url`
- **Quality**: `ad_strength`, `policy_summary`
- **Media**: `image_url`, `video_url`, `video_views`

## 🚀 Usage Examples

### JavaScript DataTable Integration

#### AdGroups DataTable
```javascript
$('#adgroupsTable').DataTable({
    processing: true,
    serverSide: true,
    ajax: {
        url: '/dsp/googleads/api/adgroups/list-table',
        type: 'POST',
        contentType: 'application/json',
        data: function(d) {
            return JSON.stringify({
                draw: d.draw,
                start: d.start,
                length: d.length,
                search: d.search.value || null,
                order: d.order.map(function(order) {
                    return {
                        column: order.column,
                        dir: order.dir
                    };
                })
            });
        }
    },
    columns: [
        { data: 'adgroup_name', name: 'adgroup_name' },
        { data: 'campaign_name', name: 'campaign_name' },
        { data: 'status', name: 'status' },
        { data: 'impressions', name: 'impressions' },
        { data: 'clicks', name: 'clicks' },
        { data: 'cost_micro', name: 'cost_micro' },
        { data: 'conversions', name: 'conversions' }
    ]
});
```

#### Ads DataTable
```javascript
$('#adsTable').DataTable({
    processing: true,
    serverSide: true,
    ajax: {
        url: '/dsp/googleads/api/ads/list-table',
        type: 'POST',
        contentType: 'application/json',
        data: function(d) {
            return JSON.stringify({
                draw: d.draw,
                start: d.start,
                length: d.length,
                search: d.search.value || null,
                order: d.order.map(function(order) {
                    return {
                        column: order.column,
                        dir: order.dir
                    };
                })
            });
        }
    },
    columns: [
        { data: 'ad_name', name: 'ad_name' },
        { data: 'adgroup_name', name: 'adgroup_name' },
        { data: 'campaign_name', name: 'campaign_name' },
        { data: 'status', name: 'status' },
        { data: 'impressions', name: 'impressions' },
        { data: 'clicks', name: 'clicks' },
        { data: 'cost_micro', name: 'cost_micro' },
        { data: 'conversions', name: 'conversions' }
    ]
});
```

### cURL Testing Examples

#### Test AdGroups API
```bash
curl -X POST http://localhost:8080/dsp/googleads/api/adgroups/list-table \
  -H "Content-Type: application/json" \
  -d '{
    "draw": 1,
    "start": 0,
    "length": 10,
    "search": "Electronics",
    "order": [{"column": 0, "dir": "asc"}]
  }'
```

#### Test Ads API
```bash
curl -X POST http://localhost:8080/dsp/googleads/api/ads/list-table \
  -H "Content-Type: application/json" \
  -d '{
    "draw": 1,
    "start": 0,
    "length": 10,
    "search": "Video",
    "order": [{"column": 0, "dir": "asc"}]
  }'
```

## ⚠️ Error Responses

### 400 Bad Request
```json
{
  "error": "Invalid request format",
  "message": "Invalid JSON format"
}
```

### 422 Unprocessable Entity
```json
{
  "error": "Validation failed",
  "message": "adgroup_id must be a valid string"
}
```

### 500 Internal Server Error
```json
{
  "error": "Internal server error",
  "message": "Failed to retrieve adgroups"
}
```

## 🔧 Implementation Notes

1. **Mock Data**: Currently returns mock data for testing. Replace with actual Google Ads API calls.
2. **Pagination**: Supports server-side pagination with `start` and `length` parameters.
3. **Search**: Case-insensitive search across multiple fields.
4. **Filtering**: Supports filtering by parent entities (Campaign, Advertiser).
5. **Sorting**: Configurable sorting by any column.
6. **Performance**: Optimized for large datasets with proper pagination.

## 🚀 Next Steps

1. **Database Integration**: Replace mock data with actual database queries
2. **Google Ads API**: Integrate with Google Ads API for real-time data
3. **Caching**: Implement caching for better performance
4. **Authentication**: Add proper authentication and authorization
5. **Rate Limiting**: Implement rate limiting for API protection

---

The API endpoints are now ready for integration with frontend DataTables and provide a consistent interface for managing Google Ads campaigns, adgroups, and ads! 🎉
