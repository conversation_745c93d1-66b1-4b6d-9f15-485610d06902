include .env
export

.PHONY: googledsp help build clean test dev docker-up docker-down

help:
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

.DEFAULT_GOAL := help

googledsp: ## Run GoogleDSP application
	templ generate && go run main.go googledsp

dev: ## Run in development mode with hot reload
	templ generate && air

build: ## Build the application
	templ generate && go build -o bin/googledsp main.go

clean: ## Clean build artifacts
	rm -rf bin/
	rm -rf tmp/

test: ## Run tests
	go test -v ./...

test-coverage: ## Run tests with coverage
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out

docker-up: ## Start Docker services
	docker-compose up -d

docker-down: ## Stop Docker services
	docker-compose down

docker-build: ## Build Docker image
	docker-compose build

docker-logs: ## View Docker logs
	docker-compose logs -f

templ-generate: ## Generate templ files
	templ generate

templ-watch: ## Watch templ files for changes
	templ generate --watch

install-deps: ## Install Go dependencies
	go mod tidy
	go mod download

install-tools: ## Install development tools
	go install github.com/a-h/templ/cmd/templ@latest
	go install github.com/cosmtrek/air@latest

lint: ## Run linter
	golangci-lint run

format: ## Format code
	go fmt ./...
	templ fmt .
