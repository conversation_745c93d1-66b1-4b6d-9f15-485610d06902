# GoogleDSP - Campaign Management Platform

GoogleDSP is a comprehensive campaign management platform that integrates with Google Ads API to provide a unified interface for managing Google DSP campaigns, performance tracking, and reporting.

## Features

- **Campaign Management**: Create, update, and manage Google Ads campaigns
- **Real-time Performance Tracking**: Monitor campaign performance with live metrics
- **Dashboard Analytics**: Comprehensive dashboard with charts and insights
- **WebSocket Integration**: Real-time updates and notifications
- **Google Ads API Integration**: Direct integration with Google Ads for campaign operations
- **MongoDB Storage**: Robust data storage for campaigns and performance data
- **Redis Caching**: Fast caching layer for improved performance
- **Docker Support**: Containerized deployment with Docker Compose

## Technology Stack

- **Go 1.23.3**: Backend API and web server
- **Fiber**: Fast HTTP web framework for Go
- **Templ**: Type-safe HTML templating engine
- **Google Ads API (Protobuf)**: Official Google Ads API client using protobuf
- **PostgreSQL**: Primary database for user data, settings, and metadata
- **MongoDB**: Performance data, analytics, and time-series data
- **Redis**: Caching, session storage, and real-time data
- **WebSocket**: Real-time communication and notifications
- **Docker**: Containerization and deployment
- **Bootstrap 5**: Frontend UI framework
- **GORM**: ORM for PostgreSQL database operations

## Prerequisites

1. **Go 1.23+**: [Download and install Go](https://go.dev/dl/)
2. **Docker & Docker Compose**: [Install Docker](https://www.docker.com/products/docker-desktop/)
3. **Google Ads API Access**:
   - Google Ads Developer Token
   - OAuth2 Client ID and Secret
   - Refresh Token
4. **Templ CLI**: Install templ for template generation
   ```bash
   go install github.com/a-h/templ/cmd/templ@latest
   ```

## Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd googledsp
cp .env.sample .env
```

### 2. Configure Environment

Edit `.env` file with your configuration:

```bash
# Application
APP_ENV=dev
FIBER_PORT=3000

# PostgreSQL Database (Primary)
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=googledsp
POSTGRES_PASSWORD=123456Xyz
POSTGRES_DATABASE=googledsp

# MongoDB Database (Analytics)
MONGODB_HOST=localhost
MONGODB_PORT=27018
MONGODB_USER=googledsp
MONGODB_PASS=123456Xyz
MONGODB_DATABASE=googledsp

# Redis
REDIS_HOST=127.0.0.1:6326
REDIS_PASSWORD=123456Xyz@
REDIS_USERNAME=googledsp

# Google Ads API (Required)
GOOGLE_ADS_CLIENT_ID=your-client-id
GOOGLE_ADS_CLIENT_SECRET=your-client-secret
GOOGLE_ADS_REFRESH_TOKEN=your-refresh-token
GOOGLE_ADS_DEVELOPER_TOKEN=your-developer-token
GOOGLE_ADS_CUSTOMER_ID=your-customer-id
```

### 3. Install Dependencies

```bash
go mod tidy
```

### 4. Generate Templates

```bash
templ generate
```

### 5. Run with Docker (Recommended)

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f app

# Stop services
docker-compose down
```

### 6. Run Locally (Development)

```bash
# Start databases with Docker
docker-compose up -d mongodb postgres redis

# Run the application
make googledsp
# or
go run main.go googledsp
```

## Available Commands

```bash
# Show help
make help

# Run application
make googledsp

# Build application
make build

# Run tests
make test

# Generate templates
make templ-generate

# Watch templates for changes
make templ-watch

# Install development tools
make install-tools

# Docker commands
make docker-up
make docker-down
make docker-build
```

## API Endpoints

### Web Routes
- `GET /` - Main dashboard
- `GET /dashboard` - Analytics dashboard
- `GET /campaigns` - Campaign list
- `GET /campaigns/create` - Create campaign form

### API Routes
- `GET /api/v1/campaigns` - Get all campaigns
- `POST /api/v1/campaigns` - Create new campaign
- `PUT /api/v1/campaigns/:id` - Update campaign
- `DELETE /api/v1/campaigns/:id` - Delete campaign
- `GET /api/v1/campaigns/:id/performance` - Get campaign performance

### Health Check
- `GET /health` - Application health status

## Project Structure

```
googledsp/
├── cmd/                    # CLI commands
│   ├── root.go            # Root command
│   └── server/            # Server command
├── conf/                  # Configuration
│   └── configs.go         # Config management
├── internal/              # Internal packages
│   ├── service_context.go # Service context
│   └── templates.go       # Template helpers
├── modules/               # Application modules
│   └── googledsp/         # GoogleDSP module
│       ├── entity/        # Data entities
│       ├── transport/     # HTTP handlers
│       └── composers/     # Service composers
├── services/              # External services
│   └── googleads/         # Google Ads API client
├── views/                 # Templ templates
│   ├── layouts/           # Layout templates
│   ├── partials/          # Partial templates
│   └── googledsp/         # Page templates
├── public/                # Static assets
├── docker/                # Docker configuration
├── docker-compose.yml     # Docker Compose config
├── Dockerfile            # Docker image config
├── Makefile              # Build commands
└── README.md             # This file
```

## Database Architecture

GoogleDSP uses a dual database architecture to optimize for different data types and access patterns:

### PostgreSQL (Primary Database)
- **User Management**: User accounts, roles, and permissions
- **Account Settings**: Google Ads account configurations and metadata
- **Campaign Settings**: Additional campaign settings not available in Google Ads API
- **Alerts & Notifications**: System alerts and user notifications
- **Audit Logs**: Complete audit trail of user actions and system changes

### MongoDB (Analytics Database)
- **Performance Data**: Campaign, keyword, and ad performance metrics
- **Time-Series Data**: Historical performance data with efficient querying
- **Audience Insights**: Demographic and behavioral data
- **Search Terms**: Search query performance data
- **Real-time Data**: WebSocket messages and live updates

### Data Flow
1. **Google Ads API**: Campaign structure and real-time performance data
2. **PostgreSQL**: User actions, settings, and metadata
3. **MongoDB**: Performance analytics and time-series data
4. **Redis**: Caching layer for frequently accessed data

## Google Ads API Setup

1. **Create Google Ads API Project**:
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one
   - Enable Google Ads API

2. **Get Developer Token**:
   - Apply for Google Ads API access
   - Get your developer token from Google Ads

3. **OAuth2 Setup**:
   - Create OAuth2 credentials
   - Get Client ID and Client Secret
   - Generate refresh token using OAuth2 flow

4. **Configure Environment**:
   - Add all credentials to `.env` file
   - Set your Google Ads Customer ID

## Development

### Hot Reload

For development with hot reload:

```bash
# Install Air
go install github.com/cosmtrek/air@latest

# Run with hot reload
make dev
```

### Testing

```bash
# Run all tests
make test

# Run tests with coverage
make test-coverage
```

### Database Management

Access database management tools:

```bash
# PostgreSQL (via psql)
docker exec -it googledsp-postgres psql -U googledsp -d googledsp

# MongoDB Express (http://localhost:8081)
docker-compose --profile tools up mongo-express

# Redis Commander (http://localhost:8082)
docker-compose --profile tools up redis-commander

# pgAdmin for PostgreSQL (optional)
# Add pgAdmin service to docker-compose.yml if needed
```

### Database Migrations

```bash
# PostgreSQL migrations are handled automatically via GORM
# MongoDB indexes are created on application startup

# Manual database operations
docker exec -it googledsp-postgres psql -U googledsp -d googledsp -f /docker-entrypoint-initdb.d/init-postgres.sql
```

## Deployment

### Production Deployment

1. **Build Docker Image**:
   ```bash
   docker-compose build app
   ```

2. **Deploy with Docker Compose**:
   ```bash
   docker-compose -f docker-compose.yml up -d
   ```

3. **Environment Variables**:
   - Set production environment variables
   - Use secure passwords and tokens
   - Configure proper network settings

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation

## Changelog

### v1.0.0
- Initial release
- Google Ads API integration
- Campaign management
- Performance tracking
- Docker support
