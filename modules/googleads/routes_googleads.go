package googleads

import (
	"googledsp/modules/googleads/composers"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"

	adgroupR "googledsp/modules/googleads/adgroups/routes"
	adR "googledsp/modules/googleads/ads/routes"
	campaignR "googledsp/modules/googleads/campaigns/routes"
	keywordR "googledsp/modules/googleads/keywords/routes"
	dashboardAPIR "googledsp/modules/googleads/routes"
)

func SetupRoutesGoogleAds(app *fiber.App, serviceCtx sctx.ServiceContext, store *session.Store, midds ...fiber.Handler) {
	compDash := composers.ComposerGoogleAdsService(serviceCtx)

	app.Get("/", compDash.IndexHdl())

	// Setup dashboard API routes for HTMX
	dashboardAPIR.SetupDashboardAPIRoutes(app, serviceCtx)

	// Setup campaign API routes for HTMX
	campaignR.SetupCampaignAPIRoutes(app, serviceCtx)

	campaignR.SetupRoutesCampaign(app, serviceCtx, store, midds...)
	adgroupR.SetupRoutesAdgroup(app, serviceCtx, store, midds...)
	adR.SetupRoutesAd(app, serviceCtx, store, midds...)
	keywordR.SetupRoutesKeyword(app, serviceCtx, store, midds...)
}
