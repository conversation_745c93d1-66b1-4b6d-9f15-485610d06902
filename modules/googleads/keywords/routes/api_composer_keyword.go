package routes

import (
	"googledsp/conf"
	"googledsp/modules/googleads/keywords/repository/mongo"
	"googledsp/modules/googleads/keywords/transport/api"
	"googledsp/modules/googleads/keywords/usecase"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"
	"github.com/dev-networldasia/dspgos/sctx/configs"

	googleAdsReportR "googledsp/modules/googleads/adserver_report/repository/mongo"
)

func ComposerKeywordApiService(serviceCtx sctx.ServiceContext) api.KeywordApiInterface {
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	mongoAdserverReportDB := serviceCtx.MustGet(conf.KeyCompReportMongoDB).(mongodb.MongoComponent).GetDatabase()

	// Create repositories
	keywordRepo := mongo.NewKeywordRepo(mongoDB)
	googleAdsReportRepo := googleAdsReportR.NewTikTokReportDetailRepo(mongoAdserverReportDB)

	param := usecase.ParamKeywordUsc{
		KeywordRepo:         keywordRepo,
		GoogleAdsReportRepo: googleAdsReportRepo,
	}
	keywordUsc := usecase.NewKeywordUsc(param)
	keywordApi := api.NewApiKeyword(keywordUsc)
	return keywordApi
}
