package routes

import (
	"googledsp/modules/googleads/keywords/transport/handlers"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

type Composer<PERSON>eywordHdl interface {
	ListKeywordHdl(store *session.Store) fiber.Handler
	EditKeywordHdl(store *session.Store) fiber.Handler
}

func ComposerKeywordService(serviceCtx sctx.ServiceContext) ComposerKeywordHdl {

	hdl := handlers.NewKeywordHdl()
	return hdl
}
