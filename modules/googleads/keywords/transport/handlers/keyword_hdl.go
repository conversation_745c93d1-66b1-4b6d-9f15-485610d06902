package handlers

import (
	keywordV "googledsp/views/ggads/keywords"

	"github.com/dev-networldasia/dspgos/gos/templates"
	"github.com/dev-networldasia/dspgos/gos/utils"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

type keywordHdl struct {
}

func NewKeywordHdl() *keywordHdl {
	return &keywordHdl{}
}

func (h *keywordHdl) ListKeywordHdl(store *session.Store) fiber.Handler {
	return func(c *fiber.Ctx) error {
		flashMsg := utils.GetFlashMessage(c, store, "keyword_list_flash_msg")
		msg := ""
		if _, ok := flashMsg.(string); ok {
			msg = flashMsg.(string)
		}

		return templates.Render(c, keywordV.ListTableKeyword(&keywordV.ListTableKeywordLayoutData{
			FlashMsg: msg,
		}))
	}
}

func (h *keywordHdl) EditKeywordHdl(store *session.Store) fiber.Handler {
	return func(c *fiber.Ctx) error {
		flashMsg := utils.GetFlashMessage(c, store, "keyword_list_flash_msg")
		msg := ""
		if _, ok := flashMsg.(string); ok {
			msg = flashMsg.(string)
		}

		return templates.Render(c, keywordV.ListTableKeyword(&keywordV.ListTableKeywordLayoutData{
			FlashMsg: msg,
		}))
	}
}
