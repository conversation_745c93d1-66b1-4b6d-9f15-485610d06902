package api

import (
	"googledsp/modules/googleads/keywords/transport/requests"
	"googledsp/modules/googleads/keywords/transport/responses"
	"googledsp/modules/googleads/keywords/usecase"
	"net/http"

	"github.com/gofiber/fiber/v2"
)

type ApiKeyword struct {
	usc usecase.KeywordUscInterface
}

func NewApiKeyword(usc usecase.KeywordUscInterface) *ApiKeyword {
	return &ApiKeyword{
		usc: usc,
	}
}

func (api *ApiKeyword) ListTableKeywordsApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		ctx := c.Context()

		var req requests.ListTableKeywordReq
		if err := c.BodyP<PERSON>er(&req); err != nil {
			return c.Status(http.StatusBadRequest).JSON(fiber.Map{
				"error":   "Invalid request format",
				"message": err.Error(),
			})
		}

		// Validate request
		if err := req.Validate(); err != nil {
			return c.Status(http.StatusUnprocessableEntity).JSON(fiber.Map{
				"error":   "Validation failed",
				"message": err.Error(),
			})
		}

		// Set sorting and time range
		req.SetSortField()
		req.SetSortOrder()
		if err := req.SetTimeRange(); err != nil {
			return c.Status(http.StatusBadRequest).JSON(fiber.Map{
				"error":   "Invalid time range",
				"message": err.Error(),
			})
		}

		// Get keywords from use case
		keywords, total, err := api.usc.ListTableKeywords(ctx, &req)
		if err != nil {
			// TODO: Add proper logging when logger is available
			return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
				"error":   "Internal server error",
				"message": "Failed to retrieve keywords",
			})
		}

		// Prepare response
		resp := responses.DatatableKeywordResp{
			Draw:            req.Draw,
			Data:            keywords,
			RecordsTotal:    total,
			RecordsFiltered: total,
		}

		return c.JSON(resp)
	}
}
