package requests

import (
	"googledsp/modules/googleads/keywords/common/consts"
	"reflect"
	"time"
)

type ListTableKeywordReq struct {
	Draw        int     `json:"draw" form:"draw"`
	Length      int64   `json:"length" form:"length"`
	Start       int64   `json:"start" form:"start"`
	SearchValue *string `json:"search" form:"search"`
	Order       []Order `json:"order"`
	SortField   string  `json:"-" form:"-"`
	SortOrder   int     `json:"-" form:"-"`

	Time      []string   `json:"time,omitempty" form:"time"`
	StartTime *time.Time `json:"start_time" form:"-"`
	EndTime   *time.Time `json:"end_time" form:"-"`

	KeywordId    string `json:"keywordId,omitempty" form:"keywordId" validate:"omitempty,number,min=10,max=30"`
	AdGroupId    string `json:"adGroupId,omitempty" form:"adGroupId" validate:"omitempty,number,min=10,max=30"`
	CampaignId   string `json:"campaignId,omitempty" form:"campaignId" validate:"omitempty,number,min=10,max=30"`
	AdvertiserID string `json:"advertiser_id,omitempty" form:"advertiserId" validate:"omitempty,number,min=10,max=30"`
}

type Order struct {
	Column int    `json:"column" form:"column"`
	Dir    string `json:"dir" form:"dir"`
}

func (req *ListTableKeywordReq) Validate() error {
	if reflect.TypeOf(req.SearchValue).Kind() == reflect.String {
		return nil
	}

	if reflect.TypeOf(req.KeywordId).Kind() != reflect.String && req.KeywordId != "" {
		return consts.ErrKeywordIdValidate
	}

	if reflect.TypeOf(req.AdGroupId).Kind() != reflect.String && req.AdGroupId != "" {
		return consts.ErrAdGroupIdValidate
	}

	if reflect.TypeOf(req.CampaignId).Kind() != reflect.String && req.CampaignId != "" {
		return consts.ErrCampaignIdValidate
	}

	if reflect.TypeOf(req.AdvertiserID).Kind() != reflect.String && req.AdvertiserID != "" {
		return consts.ErrAdvertiserIdValidate
	}

	return nil
}

func (req *ListTableKeywordReq) GetSortField() string {
	if len(req.Order) == 0 {
		return "keyword_text"
	}

	switch req.Order[0].Column {
	case 0:
		return "keyword_text"
	case 1:
		return "adgroup_name"
	case 2:
		return "campaign_name"
	case 3:
		return "status"
	case 4:
		return "impressions"
	case 5:
		return "clicks"
	case 6:
		return "cost_micro"
	case 7:
		return "conversions"
	default:
		return "keyword_text"
	}
}

func (req *ListTableKeywordReq) GetSortOrder() int {
	if len(req.Order) == 0 {
		return 1 // ASC
	}

	if req.Order[0].Dir == "desc" {
		return -1 // DESC
	}
	return 1 // ASC
}

func (req *ListTableKeywordReq) GetLimit() int64 {
	if req.Length <= 0 {
		return 10
	}
	if req.Length > 100 {
		return 100
	}
	return req.Length
}

func (req *ListTableKeywordReq) GetSkip() int64 {
	if req.Start < 0 {
		return 0
	}
	return req.Start
}

func (req *ListTableKeywordReq) GetSearchValue() string {
	if req.SearchValue == nil {
		return ""
	}
	return *req.SearchValue
}

func (req *ListTableKeywordReq) SetSortField() {
	req.SortField = req.GetSortField()
}

func (req *ListTableKeywordReq) SetSortOrder() {
	req.SortOrder = req.GetSortOrder()
}

func (req *ListTableKeywordReq) SetTimeRange() error {
	if len(req.Time) != 2 {
		// Set default time range (last 30 days)
		now := time.Now()
		req.EndTime = &now
		startTime := now.AddDate(0, 0, -30)
		req.StartTime = &startTime
		return nil
	}

	startTime, err := time.Parse("2006-01-02", req.Time[0])
	if err != nil {
		return err
	}
	req.StartTime = &startTime

	endTime, err := time.Parse("2006-01-02", req.Time[1])
	if err != nil {
		return err
	}
	req.EndTime = &endTime

	return nil
}
