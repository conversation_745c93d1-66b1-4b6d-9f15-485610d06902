package responses

type DatatableKeywordResp struct {
	Draw            int          `json:"draw"`
	Data            *[]KeywordRow `json:"data"`
	RecordsTotal    int64        `json:"recordsTotal"`
	RecordsFiltered int64        `json:"recordsFiltered"`
	Msg             string       `json:"msg,omitempty"`
}

type KeywordRow struct {
	DT_RowId       string `json:"DT_RowId,omitempty"`
	KeywordID      string `json:"keyword_id" bson:"keyword_id"`
	KeywordText    string `json:"keyword_text" bson:"keyword_text"`
	AdGroupID      string `json:"adgroup_id" bson:"adgroup_id"`
	AdGroupName    string `json:"adgroup_name" bson:"adgroup_name"`
	CampaignID     string `json:"campaign_id" bson:"campaign_id"`
	CampaignName   string `json:"campaign_name" bson:"campaign_name"`
	AdvertiserID   string `json:"advertiser_id" bson:"advertiser_id"`
	AdvertiserName string `json:"advertiser_name" bson:"advertiser_name"`
	Status         string `json:"status" bson:"status"`

	// --------------- Report fields ---------------
	AverageCost float64 `json:"average_cost" bson:"average_cost"`
	AverageCPC  float64 `json:"average_cpc" bson:"average_cpc"`
	AverageCPE  float64 `json:"average_cpe" bson:"average_cpe"`
	AverageCPM  float64 `json:"average_cpm" bson:"average_cpm"`
	AverageCPV  float64 `json:"average_cpv" bson:"average_cpv"`
	CostMicro   int64   `json:"cost_micro" bson:"cost_micro"`
	Currency    string  `json:"currency" bson:"currency"`

	ActiveViewImpressions   int64 `json:"active_view_impressions" bson:"active_view_impressions"`
	ActiveViewMeasurability int64 `json:"active_view_measurability" bson:"active_view_measurability"`
	ActiveViewViewability   int64 `json:"active_view_viewability" bson:"active_view_viewability"`
	AllConversions          int64 `json:"all_conversions" bson:"all_conversions"`
	AllConversionsValue     int64 `json:"all_conversions_value" bson:"all_conversions_value"`
	Clicks                  int64 `json:"clicks" bson:"clicks"`
	Conversions             int64 `json:"conversions" bson:"conversions"`
	Impressions             int64 `json:"impressions" bson:"impressions"`
	Engagement              int64 `json:"engagement" bson:"engagement"`
	GmailForward            int64 `json:"gmail_forward" bson:"gmail_forward"`
	GmailSaves              int64 `json:"gmail_saves" bson:"gmail_saves"`
	Interactions            int64 `json:"interactions" bson:"interactions"`

	PublisherOrganicClicks   int64 `json:"publisher_organic_clicks" bson:"publisher_organic_clicks"`
	PublisherPurchasedClicks int64 `json:"publisher_purchased_clicks" bson:"publisher_purchased_clicks"`
	PublisherUnknownClicks   int64 `json:"publisher_unknown_clicks" bson:"publisher_unknown_clicks"`

	SKAdNetworkInstalls         int64 `json:"sk_ad_network_installs" bson:"sk_ad_network_installs"`
	SkAdNetworkTotalConversions int64 `json:"sk_ad_network_total_conversions" bson:"sk_ad_network_total_conversions"`

	VideoQuartileP100Rate int64 `json:"video_quartile_p100_rate" bson:"video_quartile_p100_rate"`
	VideoQuartileP25Rate  int64 `json:"video_quartile_p25_rate" bson:"video_quartile_p25_rate"`
	VideoQuartileP50Rate  int64 `json:"video_quartile_p50_rate" bson:"video_quartile_p50_rate"`
	VideoQuartileP75Rate  int64 `json:"video_quartile_p75_rate" bson:"video_quartile_p75_rate"`
	VideoViewRate         int64 `json:"video_view_rate" bson:"video_view_rate"`
	VideoViews            int64 `json:"video_views" bson:"video_views"`

	// Keyword specific fields
	MatchType        string  `json:"match_type" bson:"match_type"`
	MaxCPC           float64 `json:"max_cpc" bson:"max_cpc"`
	QualityScore     int     `json:"quality_score" bson:"quality_score"`
	FirstPageCPC     float64 `json:"first_page_cpc" bson:"first_page_cpc"`
	TopOfPageCPC     float64 `json:"top_of_page_cpc" bson:"top_of_page_cpc"`
	SearchRankLost   string  `json:"search_rank_lost" bson:"search_rank_lost"`
	SearchImprShare  float64 `json:"search_impr_share" bson:"search_impr_share"`
	SearchExactMatch bool    `json:"search_exact_match" bson:"search_exact_match"`
}
