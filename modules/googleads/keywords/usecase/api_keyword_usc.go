package usecase

import (
	"context"
	"fmt"
	"googledsp/modules/googleads/keywords/mapping"
	"googledsp/modules/googleads/keywords/transport/requests"
	"googledsp/modules/googleads/keywords/transport/responses"
	"time"

	"go.mongodb.org/mongo-driver/bson"
)

type KeywordUsc struct {
	keywordRepo                  KeywordRepo
	googleAdsKeywordInsightsRepo GoogleAdsKeywordInsightsRepo
}

type ParamKeywordUsc struct {
	KeywordRepo                  KeywordRepo
	GoogleAdsKeywordInsightsRepo GoogleAdsKeywordInsightsRepo
}

func NewKeywordUsc(param ParamKeywordUsc) *KeywordUsc {
	return &KeywordUsc{
		keywordRepo:                  param.KeywordRepo,
		googleAdsKeywordInsightsRepo: param.GoogleAdsKeywordInsightsRepo,
	}
}

func (usc *KeywordUsc) getFilterKeywordReport(ctx context.Context, payload *requests.ListTableKeywordReq) bson.M {
	filter := bson.M{}

	if payload.StartTime != nil && payload.EndTime != nil {
		startTimeStr := (*payload.StartTime).Format("2006-01-02")
		endTimeStr := (*payload.EndTime).Format("2006-01-02")
		filter["date"] = bson.M{"$gte": startTimeStr, "$lte": endTimeStr}
	} else {
		yesterday := time.Now().AddDate(0, 0, -1)
		endOfYesterdayStr := yesterday.Format("2006-01-02")
		startOfYesterdayStr := yesterday.AddDate(0, 0, -30).Format("2006-01-02")
		filter["date"] = bson.M{"$gte": startOfYesterdayStr, "$lte": endOfYesterdayStr}
	}

	if payload.KeywordId != "" {
		filter["ad_group_criterion_keyword_text"] = bson.M{"$regex": payload.KeywordId, "$options": "i"}
	}
	if payload.AdGroupId != "" {
		filter["ad_group_id"] = payload.AdGroupId
	}
	if payload.CampaignId != "" {
		filter["campaign_id"] = payload.CampaignId
	}
	if payload.AdvertiserID != "" {
		filter["advertiser_id"] = payload.AdvertiserID
	}

	// Add search functionality
	if payload.GetSearchValue() != "" {
		searchRegex := bson.M{"$regex": payload.GetSearchValue(), "$options": "i"}
		filter["$or"] = []bson.M{
			{"ad_group_criterion_keyword_text": searchRegex},
			{"campaign_name": searchRegex},
			{"ad_group_name": searchRegex},
		}
	}

	return filter
}

func (usc *KeywordUsc) ListTableKeywords(ctx context.Context, req *requests.ListTableKeywordReq) (*[]responses.KeywordRow, int64, error) {
	reportFilter := usc.getFilterKeywordReport(ctx, req)
	keywordData, err := usc.googleAdsKeywordInsightsRepo.GetKeywordInsights(ctx, reportFilter)

	if err != nil {
		fmt.Printf("====== Keywords err ====== %+v", err)
		return nil, 0, err
	}

	fmt.Printf("\n===== Keywords len ====== %+v ", len(*keywordData))

	// Check for duplicates (for debugging)
	seen := make(map[string]bool)
	for _, data := range *keywordData {
		keywordId := data.AdGroupCriterionKeywordText + "_" + data.AdGroupID
		if seen[keywordId] {
			fmt.Println("Duplicate KeywordID:", keywordId)
		} else {
			seen[keywordId] = true
		}
	}

	dataTableRes := mapping.MapperListTableKeyword(keywordData)

	return dataTableRes, int64(len(*dataTableRes)), nil
}
