package usecase

import (
	"context"
	"fmt"
	"googledsp/modules/googleads/keywords/mapping"
	"googledsp/modules/googleads/keywords/transport/requests"
	"googledsp/modules/googleads/keywords/transport/responses"
	"time"

	"go.mongodb.org/mongo-driver/bson"
)

type KeywordUsc struct {
	keywordRepo         KeywordRepo
	googleAdsReportRepo GoogleAdsReportRepo
}

type ParamKeywordUsc struct {
	KeywordRepo         KeywordRepo
	GoogleAdsReportRepo GoogleAdsReportRepo
}

func NewKeywordUsc(param ParamKeywordUsc) *KeywordUsc {
	return &KeywordUsc{
		keywordRepo:         param.KeywordRepo,
		googleAdsReportRepo: param.GoogleAdsReportRepo,
	}
}

func (usc *KeywordUsc) getFilterKeywordReport(ctx context.Context, payload *requests.ListTableKeywordReq) bson.M {
	filter := bson.M{}

	if payload.StartTime != nil && payload.EndTime != nil {
		startTimeStr := (*payload.StartTime).Format("2006-01-02")
		endTimeStr := (*payload.EndTime).Format("2006-01-02")
		filter["date"] = bson.M{"$gte": startTimeStr, "$lte": endTimeStr}
	} else {
		yesterday := time.Now().AddDate(0, 0, -1)
		endOfYesterdayStr := yesterday.Format("2006-01-02")
		startOfYesterdayStr := yesterday.AddDate(0, 0, -30).Format("2006-01-02")
		filter["date"] = bson.M{"$gte": startOfYesterdayStr, "$lte": endOfYesterdayStr}
	}

	if payload.KeywordId != "" {
		// Since keywords are not in the current entity structure, we'll use ad_id as filter
		filter["ad_id"] = payload.KeywordId
	}
	if payload.AdGroupId != "" {
		filter["ad_group_id"] = payload.AdGroupId
	}
	if payload.CampaignId != "" {
		filter["campaign_id"] = payload.CampaignId
	}
	if payload.AdvertiserID != "" {
		filter["advertiser_id"] = payload.AdvertiserID
	}

	return filter
}

func (usc *KeywordUsc) ListTableKeywords(ctx context.Context, req *requests.ListTableKeywordReq) (*[]responses.KeywordRow, int64, error) {
	reportFilter := usc.getFilterKeywordReport(ctx, req)
	reportData, err := usc.googleAdsReportRepo.GetReportDetailByKeyword(ctx, reportFilter)

	seen := make(map[string]bool)
	for _, data := range *reportData {
		// Since keywords are not in the current entity structure, we'll use AdID as identifier
		keywordId := data.AdID
		if seen[keywordId] {
			fmt.Println("Duplicate KeywordID:", keywordId)
		} else {
			seen[keywordId] = true
		}
	}

	fmt.Printf("\n===== Keywords len ====== %+v ", len(*reportData))
	if err != nil {
		fmt.Printf("====== Keywords err ====== %+v", err)
		return nil, 0, err
	}

	dataTableRes := mapping.MapperListTableKeyword(reportData)

	return dataTableRes, int64(len(*dataTableRes)), nil
}
