package usecase

import (
	"context"
	"googledsp/modules/googleads/keywords/transport/requests"
	"googledsp/modules/googleads/keywords/transport/responses"
	"googledsp/modules/googleads/adserver_report/entity"

	"go.mongodb.org/mongo-driver/bson"
)

type KeywordRepo interface {
}

type GoogleAdsReportRepo interface {
	GetReportDetailByKeyword(ctx context.Context, filter bson.M) (*[]entity.GoogleAdReportDetailEntity, error)
}

type KeywordUscInterface interface {
	ListTableKeywords(ctx context.Context, req *requests.ListTableKeywordReq) (*[]responses.KeywordRow, int64, error)
}
