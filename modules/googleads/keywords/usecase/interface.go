package usecase

import (
	"context"
	"googledsp/modules/googleads/adserver_report/entity"
	"googledsp/modules/googleads/keywords/transport/requests"
	"googledsp/modules/googleads/keywords/transport/responses"

	"go.mongodb.org/mongo-driver/bson"
)

type KeywordRepo interface {
}

type GoogleAdsKeywordInsightsRepo interface {
	GetKeywordInsights(ctx context.Context, filter bson.M) (*[]entity.GoogleAdKeywordInsightsEntity, error)
	GetKeywordCount(ctx context.Context, filter bson.M) (int64, error)
}

type KeywordUscInterface interface {
	ListTableKeywords(ctx context.Context, req *requests.ListTableKeywordReq) (*[]responses.KeywordRow, int64, error)
}
