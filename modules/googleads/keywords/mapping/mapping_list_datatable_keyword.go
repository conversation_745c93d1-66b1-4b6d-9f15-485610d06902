package mapping

import (
	"encoding/json"
	"fmt"
	googleAdsReport "googledsp/modules/googleads/adserver_report/entity"
	"googledsp/modules/googleads/keywords/transport/responses"
)

func MapperListTableKeyword(reportData *[]googleAdsReport.GoogleAdReportDetailEntity) *[]responses.KeywordRow {
	if reportData == nil {
		return &[]responses.KeywordRow{}
	}

	result := make([]responses.KeywordRow, 0, len(*reportData))

	for _, report := range *reportData {
		// Marshal report to JSON, then unmarshal to KeywordRow
		jsonData, _ := json.Marshal(report)

		var mapped responses.KeywordRow
		json.Unmarshal(jsonData, &mapped)

		// Map specific fields that might have different names
		// Since keywords are not in the current entity structure, we'll use ad data as a placeholder
		mapped.DT_RowId = fmt.Sprintf("keyword_%s", report.AdID)
		mapped.KeywordID = report.AdID                                    // Using AdID as KeywordID placeholder
		mapped.KeywordText = fmt.Sprintf("Keyword for %s", report.AdName) // Generate keyword text from ad name
		mapped.AdGroupID = report.AdGroupID
		mapped.AdGroupName = report.AdGroupName
		mapped.CampaignID = report.CampaignID
		mapped.CampaignName = report.CampaignName
		mapped.AdvertiserID = report.AdvertiserID
		mapped.AdvertiserName = report.AdvertiserName
		mapped.CostMicro = report.CostMicros

		// Set default values for Keyword specific fields if not available
		if mapped.Status == "" {
			mapped.Status = "ENABLED" // Default status
		}
		if mapped.MatchType == "" {
			mapped.MatchType = "BROAD" // Default match type
		}
		if mapped.Currency == "" {
			mapped.Currency = "VND" // Default currency
		}

		// Set default keyword text if not available
		if mapped.KeywordText == "" {
			mapped.KeywordText = "Keyword " + mapped.KeywordID // Default keyword text
		}

		result = append(result, mapped)
	}

	return &result
}
