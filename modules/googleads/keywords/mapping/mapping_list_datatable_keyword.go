package mapping

import (
	"encoding/json"
	"fmt"
	"googledsp/modules/googleads/adserver_report/entity"
	"googledsp/modules/googleads/keywords/transport/responses"
)

func MapperListTableKeyword(keywordData *[]entity.GoogleAdKeywordInsightsEntity) *[]responses.KeywordRow {
	if keywordData == nil {
		return &[]responses.KeywordRow{}
	}

	result := make([]responses.KeywordRow, 0, len(*keywordData))

	for _, keyword := range *keywordData {
		// Marshal keyword to JSON, then unmarshal to KeywordRow
		jsonData, _ := json.Marshal(keyword)

		var mapped responses.KeywordRow
		json.Unmarshal(jsonData, &mapped)

		// Map specific fields from keyword insights entity
		mapped.DT_RowId = fmt.Sprintf("keyword_%s_%s", keyword.AdGroupCriterionKeywordText, keyword.AdGroupID)
		mapped.KeywordID = fmt.Sprintf("%s_%s", keyword.AdGroupCriterionKeywordText, keyword.AdGroupID) // Unique identifier
		mapped.KeywordText = keyword.AdGroupCriterionKeywordText
		mapped.AdGroupID = keyword.AdGroupID
		mapped.AdGroupName = keyword.AdGroupName
		mapped.CampaignID = keyword.CampaignID
		mapped.CampaignName = keyword.CampaignName
		mapped.AdvertiserID = keyword.AdvertiserID
		mapped.AdvertiserName = keyword.AdvertiserName

		// Map performance metrics
		mapped.Clicks = keyword.Clicks
		mapped.Impressions = keyword.Impressions
		mapped.Conversions = int64(keyword.Conversions)
		mapped.AverageCPC = keyword.AverageCPC
		mapped.Engagement = keyword.Engagements
		mapped.Interactions = keyword.Interactions

		// Map keyword specific fields
		mapped.MatchType = keyword.AdGroupCriterionKeywordMatchType
		if mapped.MatchType == "" {
			mapped.MatchType = keyword.KeywordMatchTypeCategory
		}
		mapped.SearchImprShare = keyword.SearchTopImpressionShare

		// Set default values
		mapped.Status = "ENABLED"                                                        // Default status since it's not in the entity
		mapped.Currency = "VND"                                                          // Default currency
		mapped.CostMicro = int64(keyword.AverageCPC * float64(keyword.Clicks) * 1000000) // Estimate cost in micros

		// Calculate CTR if not available
		if keyword.CTR > 0 {
			mapped.AverageCost = keyword.CTR
		} else if keyword.Impressions > 0 {
			mapped.AverageCost = float64(keyword.Clicks) / float64(keyword.Impressions) * 100
		}

		result = append(result, mapped)
	}

	return &result
}
