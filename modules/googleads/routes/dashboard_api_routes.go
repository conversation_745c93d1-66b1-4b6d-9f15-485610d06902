package routes

import (
	"googledsp/modules/googleads/transport/handlers"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/gofiber/fiber/v2"
)

type DashboardAPIComposer interface {
	GetMetricsAPI() fiber.Handler
	GetPerformanceChartAPI() fiber.Handler
	UpdateDateRangeAPI() fiber.Handler
	ToggleCompareAPI() fiber.Handler
	SetPresetAPI() fiber.Handler
	RefreshDashboardAPI() fiber.Handler
	GetCampaignPerformanceAPI() fiber.Handler
}

func ComposerDashboardAPIService(serviceCtx sctx.ServiceContext) DashboardAPIComposer {
	// For now, pass nil client - will use mock data
	hdl := handlers.NewDashboardAPIHdl(nil)
	return hdl
}

func SetupDashboardAPIRoutes(app *fiber.App, serviceCtx sctx.ServiceContext) {
	composer := ComposerDashboardAPIService(serviceCtx)

	// API routes for dashboard HTMX requests
	api := app.Group("/api/dashboard")

	// Metrics endpoints
	api.Get("/metrics", composer.GetMetricsAPI())
	api.Get("/performance-chart", composer.GetPerformanceChartAPI())
	api.Get("/campaign-performance", composer.GetCampaignPerformanceAPI())

	// Filter and control endpoints
	api.Get("/update-date-range", composer.UpdateDateRangeAPI())
	api.Get("/toggle-compare", composer.ToggleCompareAPI())
	api.Get("/set-preset", composer.SetPresetAPI())
	api.Get("/refresh", composer.RefreshDashboardAPI())
}
