package usecase

import (
	"context"
	"fmt"
	"googledsp/modules/googleads/campaigns/mapping"
	"googledsp/modules/googleads/campaigns/transport/requests"
	"googledsp/modules/googleads/campaigns/transport/responses"
	"time"

	"github.com/dev-networldasia/dspgos/sctx"
	"go.mongodb.org/mongo-driver/bson"
)

type apiCampaignUsc struct {
	campaignRepo        CampaignRepo
	googleAdsReportRepo GoogleAdsReportRepo
	logger              sctx.Logger
}

type ParamCampaignUsc struct {
	CampaignRepo        CampaignRepo
	GoogleAdsReportRepo GoogleAdsReportRepo
	Logger              sctx.Logger
}

func NewCampaignUsc(param ParamCampaignUsc) *apiCampaignUsc {
	return &apiCampaignUsc{
		campaignRepo:        param.CampaignRepo,
		googleAdsReportRepo: param.GoogleAdsReportRepo,
		logger:              param.Logger,
	}
}

/**
 * Get report detail by ad campaign
 */
func (usc *apiCampaignUsc) getFilterCampaignReport(ctx context.Context, payload *requests.ListTableCampaignReq) bson.M {
	filter := bson.M{}

	if payload.StartTime != nil && payload.EndTime != nil {
		startTimeStr := (*payload.StartTime).Format("2006-01-02")
		endTimeStr := (*payload.EndTime).Format("2006-01-02")
		filter["date"] = bson.M{"$gte": startTimeStr, "$lte": endTimeStr}
	} else {
		yesterday := time.Now().AddDate(0, 0, -1)
		endOfYesterdayStr := yesterday.Format("2006-01-02")
		startOfYesterdayStr := yesterday.AddDate(0, 0, -30).Format("2006-01-02")
		filter["date"] = bson.M{"$gte": startOfYesterdayStr, "$lte": endOfYesterdayStr}
	}

	// jsonData, _ := json.MarshalIndent(filter, "", "  ")
	// fmt.Printf("getMetricCampaignReport: filter: %v\n", string(jsonData))

	return filter
}

/***
 * List campaigns
 */
func (usc *apiCampaignUsc) ListTableCampaigns(ctx context.Context, payload *requests.ListTableCampaignReq) (*responses.DatatableCampaignResq, error) {
	reportFilter := usc.getFilterCampaignReport(ctx, payload)
	reportData, err := usc.googleAdsReportRepo.GetReportDetailByAdCampaign(ctx, reportFilter)
	seen := make(map[string]bool)
	for _, data := range *reportData {
		if seen[data.CampaignID] {
			fmt.Println("Duplicate CampaignID:", data.CampaignID)
		} else {
			seen[data.CampaignID] = true
		}
	}

	fmt.Printf("\n===== len ====== %+v ", len(*reportData))
	if err != nil {
		usc.logger.Error(err)
		fmt.Printf("====== err ====== %+v", err)
		return nil, err
	}

	dataTableRes := mapping.MapperListTableCampaign(reportData)

	// //total all
	// total, err := usc.repo.CountCampaignRepo(ctx, bson.M{})
	// if err != nil {
	// 	return nil, err
	// }

	// // total filtered
	// totalFiltered := total
	// if len(filter) != 0 {
	// 	totalFiltered, err = usc.repo.CountCampaignRepo(ctx, filter)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// }

	return &responses.DatatableCampaignResq{
		Draw:            payload.Draw,
		Data:            dataTableRes,
		RecordsTotal:    0,
		RecordsFiltered: 0,
	}, nil
}
