package api

import (
	"fmt"
	"googledsp/modules/googleads/campaigns/transport/requests"
	"googledsp/modules/googleads/campaigns/transport/responses"

	"github.com/dev-networldasia/dspgos/sctx/core"
	"github.com/gofiber/fiber/v2"
)

func response(c *fiber.Ctx, msg string, code ...int) error {
	status := fiber.StatusOK
	if len(code) > 0 {
		status = code[0]
	}
	return c.Status(status).JSON(core.ResponseData(map[string]interface{}{
		"msg":  msg,
		"data": nil,
	}))
}

func (api *apiCampaign) ListTableCampaignsApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var (
			payload requests.ListTableCampaignReq
			resData = responses.DatatableCampaignResq{}
		)

		// Parse payload
		if err := c.BodyParser(&payload); err != nil {
			fmt.Printf("====== err ====== %+v", err)
			return response(c, err.Error())
		}
		resData.Draw = payload.Draw

		// Validate payload
		if err := payload.Validate(); err != nil {
			fmt.Printf("====== err ====== %+v", err)
			return response(c, err.Error())
		}

		// Call usecase
		campDatatable, err := api.usc.ListTableCampaigns(c.Context(), &payload)
		if err != nil {
			resData.Msg = err.Error()
			return c.JSON(resData)
		}

		return c.JSON(campDatatable)
	}

}
