package api

import (
	"context"
	"googledsp/modules/googleads/campaigns/transport/requests"
	"googledsp/modules/googleads/campaigns/transport/responses"
)

type ApiCampaignUsc interface {
	ListTableCampaigns(ctx context.Context, payload *requests.ListTableCampaignReq) (*responses.DatatableCampaignResq, error)
}

type apiCampaign struct {
	usc ApiCampaignUsc
}

func NewApiCampaign(usc ApiCampaignUsc) *apiCampaign {
	return &apiCampaign{
		usc: usc,
	}
}
