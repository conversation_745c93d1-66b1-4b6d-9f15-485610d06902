package responses

type DatatableCampaignResq struct {
	Draw            int            `json:"draw"`
	Data            *[]CampaignRow `json:"data"`
	RecordsTotal    int64          `json:"recordsTotal"`
	RecordsFiltered int64          `json:"recordsFiltered"`
	Msg             string         `json:"msg,omitempty"`
}

type CampaignRow struct {
	DT_RowId       string `json:"DT_RowId,omitempty"`
	CampaignID     string `json:"campaign_id" bson:"campaign_id"`
	CampaignName   string `json:"campaign_name" bson:"campaign_name"`
	AdvertiserID   string `json:"advertiser_id" bson:"advertiser_id"`
	AdvertiserName string `json:"advertiser_name" bson:"advertiser_name"`

	// --------------- Report fields ---------------
	AverageCost float64 `json:"average_cost" bson:"average_cost"`
	AverageCPC  float64 `json:"average_cpc" bson:"average_cpc"`
	AverageCPE  float64 `json:"average_cpe" bson:"average_cpe"`
	AverageCPM  float64 `json:"average_cpm" bson:"average_cpm"`
	AverageCPV  float64 `json:"average_cpv" bson:"average_cpv"`
	CostMicro   int64   `json:"cost_micro" bson:"cost_micro"`
	Currency    string  `json:"currency" bson:"currency"`

	ActiveViewImpressions   int64 `json:"active_view_impressions" bson:"active_view_impressions"`
	ActiveViewMeasurability int64 `json:"active_view_measurability" bson:"active_view_measurability"`
	ActiveViewViewability   int64 `json:"active_view_viewability" bson:"active_view_viewability"`
	AllConversions          int64 `json:"all_conversions" bson:"all_conversions"`
	AllConversionsValue     int64 `json:"all_conversions_value" bson:"all_conversions_value"`
	Clicks                  int64 `json:"clicks" bson:"clicks"`
	Conversions             int64 `json:"conversions" bson:"conversions"`
	Impressions             int64 `json:"impressions" bson:"impressions"`
	Engagement              int64 `json:"engagement" bson:"engagement"`
	GmailForward            int64 `json:"gmail_forward" bson:"gmail_forward"`
	GmailSaves              int64 `json:"gmail_saves" bson:"gmail_saves"`
	Interactions            int64 `json:"interactions" bson:"interactions"`

	PublisherOrganicClicks   int64 `json:"publisher_organic_clicks" bson:"publisher_organic_clicks"`
	PublisherPurchasedClicks int64 `json:"publisher_purchased_clicks" bson:"publisher_purchased_clicks"`
	PublisherUnknownClicks   int64 `json:"publisher_unknown_clicks" bson:"publisher_unknown_clicks"`

	SKAdNetworkInstalls         int64 `json:"sk_ad_network_installs" bson:"sk_ad_network_installs"`
	SkAdNetworkTotalConversions int64 `json:"sk_ad_network_total_conversions" bson:"sk_ad_network_total_conversions"`

	VideoQuartileP100Rate int64 `json:"video_quartile_p100_rate" bson:"video_quartile_p100_rate"`
	VideoQuartileP25Rate  int64 `json:"video_quartile_p25_rate" bson:"video_quartile_p25_rate"`
	VideoQuartileP50Rate  int64 `json:"video_quartile_p50_rate" bson:"video_quartile_p50_rate"`
	VideoQuartileP75Rate  int64 `json:"video_quartile_p75_rate" bson:"video_quartile_p75_rate"`
	VideoViewRate         int64 `json:"video_view_rate" bson:"video_view_rate"`
	VideoViews            int64 `json:"video_views" bson:"video_views"`
}
