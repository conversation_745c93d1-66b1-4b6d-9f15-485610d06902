package handlers

import (
	"net/http/httptest"
	"testing"

	"github.com/gofiber/fiber/v2"
	"github.com/stretchr/testify/assert"
)

func TestCampaignAPIHdl_GetCampaignListAPI(t *testing.T) {
	// Create a new Fiber app
	app := fiber.New()
	
	// Create handler
	handler := NewCampaignAPIHdl()
	
	// Setup route
	app.Get("/api/campaigns/list", handler.GetCampaignListAPI())
	
	// Create test request
	req := httptest.NewRequest("GET", "/api/campaigns/list", nil)
	
	// Perform request
	resp, err := app.Test(req)
	
	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, 200, resp.StatusCode)
}

func TestCampaignAPIHdl_FilterCampaignsAPI(t *testing.T) {
	// Create a new Fiber app
	app := fiber.New()
	
	// Create handler
	handler := NewCampaignAPIHdl()
	
	// Setup route
	app.Get("/api/campaigns/filter", handler.FilterCampaignsAPI())
	
	// Create test request with query parameters
	req := httptest.NewRequest("GET", "/api/campaigns/filter?search_term=Summer&status=ACTIVE", nil)
	
	// Perform request
	resp, err := app.Test(req)
	
	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, 200, resp.StatusCode)
}

func TestCampaignAPIHdl_RefreshCampaignsAPI(t *testing.T) {
	// Create a new Fiber app
	app := fiber.New()
	
	// Create handler
	handler := NewCampaignAPIHdl()
	
	// Setup route
	app.Get("/api/campaigns/refresh", handler.RefreshCampaignsAPI())
	
	// Create test request
	req := httptest.NewRequest("GET", "/api/campaigns/refresh", nil)
	
	// Perform request
	resp, err := app.Test(req)
	
	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, 200, resp.StatusCode)
}

func TestCampaignAPIHdl_ExportCampaignsAPI(t *testing.T) {
	// Create a new Fiber app
	app := fiber.New()
	
	// Create handler
	handler := NewCampaignAPIHdl()
	
	// Setup route
	app.Get("/api/campaigns/export", handler.ExportCampaignsAPI())
	
	// Create test request
	req := httptest.NewRequest("GET", "/api/campaigns/export?ids=12345,67890", nil)
	
	// Perform request
	resp, err := app.Test(req)
	
	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, 200, resp.StatusCode)
	assert.Equal(t, "text/csv", resp.Header.Get("Content-Type"))
	assert.Contains(t, resp.Header.Get("Content-Disposition"), "campaigns.csv")
}

func TestCampaignAPIHdl_UpdateCampaignStatusAPI(t *testing.T) {
	// Create a new Fiber app
	app := fiber.New()
	
	// Create handler
	handler := NewCampaignAPIHdl()
	
	// Setup route
	app.Post("/api/campaigns/:id/status", handler.UpdateCampaignStatusAPI())
	
	// Create test request
	req := httptest.NewRequest("POST", "/api/campaigns/12345/status", nil)
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	
	// Perform request
	resp, err := app.Test(req)
	
	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, 200, resp.StatusCode)
}

func TestCampaignAPIHdl_DuplicateCampaignAPI(t *testing.T) {
	// Create a new Fiber app
	app := fiber.New()
	
	// Create handler
	handler := NewCampaignAPIHdl()
	
	// Setup route
	app.Post("/api/campaigns/:id/duplicate", handler.DuplicateCampaignAPI())
	
	// Create test request
	req := httptest.NewRequest("POST", "/api/campaigns/12345/duplicate", nil)
	
	// Perform request
	resp, err := app.Test(req)
	
	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, 200, resp.StatusCode)
}

func TestCampaignAPIHdl_DeleteCampaignAPI(t *testing.T) {
	// Create a new Fiber app
	app := fiber.New()
	
	// Create handler
	handler := NewCampaignAPIHdl()
	
	// Setup route
	app.Delete("/api/campaigns/:id", handler.DeleteCampaignAPI())
	
	// Create test request
	req := httptest.NewRequest("DELETE", "/api/campaigns/12345", nil)
	
	// Perform request
	resp, err := app.Test(req)
	
	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, 200, resp.StatusCode)
}
