package handlers

import (
	campaignV "googledsp/views/ggads/campaigns"

	"github.com/dev-networldasia/dspgos/gos/templates"
	"github.com/dev-networldasia/dspgos/gos/utils"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

type campaignHdl struct {
}

func NewCampaignHdl() *campaignHdl {
	return &campaignHdl{}
}

func (h *campaignHdl) ListCampaignHdl(store *session.Store) fiber.Handler {
	return func(c *fiber.Ctx) error {
		flashMsg := utils.GetFlashMessage(c, store, "campaign_list_flash_msg")
		msg := ""
		if _, ok := flashMsg.(string); ok {
			msg = flashMsg.(string)
		}

		return templates.Render(c, campaignV.ListTableCampaign(&campaignV.ListTableCampaignLayoutData{
			FlashMsg: msg,
		}))
	}
}

func (h *campaignHdl) EditCampaignHdl(store *session.Store) fiber.Handler {
	return func(c *fiber.Ctx) error {
		flashMsg := utils.GetFlashMessage(c, store, "campaign_list_flash_msg")
		msg := ""
		if _, ok := flashMsg.(string); ok {
			msg = flashMsg.(string)
		}

		return templates.Render(c, campaignV.ListTableCampaign(&campaignV.ListTableCampaignLayoutData{
			FlashMsg: msg,
		}))
	}
}
