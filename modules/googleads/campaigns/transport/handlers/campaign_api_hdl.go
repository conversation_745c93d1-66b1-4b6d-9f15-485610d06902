package handlers

import (
	"googledsp/views/ggads/campaigns/components"
	"googledsp/views/ggads/campaigns/models"
	"strconv"
	"time"

	"github.com/dev-networldasia/dspgos/gos/templates"
	"github.com/gofiber/fiber/v2"
)

type campaignAPIHdl struct {
	// googleAdsClient can be added later when needed
}

func NewCampaignAPIHdl() *campaignAPIHdl {
	return &campaignAPIHdl{}
}

// GetCampaignListAPI returns campaign list data for HTMX requests
func (h *campaignAPIHdl) GetCampaignListAPI() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Get filter parameters
		filter := h.parseFilterFromQuery(c)

		// Get campaigns data (mock for now)
		reportData := h.getMockCampaignReportData(filter)

		return templates.Render(c, components.CampaignReport(reportData))
	}
}

// FilterCampaignsAPI handles campaign filtering
func (h *campaignAPIHdl) FilterCampaignsAPI() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Parse filter from form data
		filter := h.parseFilterFromForm(c)

		// Get filtered campaigns data
		reportData := h.getMockCampaignReportData(filter)

		return templates.Render(c, components.CampaignReport(reportData))
	}
}

// RefreshCampaignsAPI refreshes campaign data
func (h *campaignAPIHdl) RefreshCampaignsAPI() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Force refresh from Google Ads API
		filter := h.parseFilterFromQuery(c)
		reportData := h.getMockCampaignReportData(filter)
		reportData.LastUpdated = time.Now().Format("2006-01-02 15:04:05")

		return templates.Render(c, components.CampaignReport(reportData))
	}
}

// UpdateCampaignStatusAPI updates campaign status
func (h *campaignAPIHdl) UpdateCampaignStatusAPI() fiber.Handler {
	return func(c *fiber.Ctx) error {
		_ = c.Params("id")        // campaignID - will be used when implementing real API
		_ = c.FormValue("status") // newStatus - will be used when implementing real API

		// TODO: Update campaign status in Google Ads API
		// For now, just return updated data

		filter := &models.CampaignFilter{
			SortBy:    "name",
			SortOrder: "asc",
			Page:      1,
			Limit:     25,
		}

		reportData := h.getMockCampaignReportData(filter)

		// Add success message
		c.Set("HX-Trigger", `{"showToast": {"message": "Campaign status updated successfully", "type": "success"}}`)

		return templates.Render(c, components.CampaignReport(reportData))
	}
}

// DuplicateCampaignAPI duplicates a campaign
func (h *campaignAPIHdl) DuplicateCampaignAPI() fiber.Handler {
	return func(c *fiber.Ctx) error {
		_ = c.Params("id") // campaignID - will be used when implementing real API

		// TODO: Duplicate campaign in Google Ads API
		// For now, just return updated data

		filter := &models.CampaignFilter{
			SortBy:    "name",
			SortOrder: "asc",
			Page:      1,
			Limit:     25,
		}

		reportData := h.getMockCampaignReportData(filter)

		// Add success message
		c.Set("HX-Trigger", `{"showToast": {"message": "Campaign duplicated successfully", "type": "success"}}`)

		return templates.Render(c, components.CampaignReport(reportData))
	}
}

// DeleteCampaignAPI deletes a campaign
func (h *campaignAPIHdl) DeleteCampaignAPI() fiber.Handler {
	return func(c *fiber.Ctx) error {
		_ = c.Params("id") // campaignID - will be used when implementing real API

		// TODO: Delete campaign in Google Ads API
		// For now, just return updated data

		filter := &models.CampaignFilter{
			SortBy:    "name",
			SortOrder: "asc",
			Page:      1,
			Limit:     25,
		}

		reportData := h.getMockCampaignReportData(filter)

		// Add success message
		c.Set("HX-Trigger", `{"showToast": {"message": "Campaign deleted successfully", "type": "success"}}`)

		return templates.Render(c, components.CampaignReport(reportData))
	}
}

// ExportCampaignsAPI exports campaigns to CSV
func (h *campaignAPIHdl) ExportCampaignsAPI() fiber.Handler {
	return func(c *fiber.Ctx) error {
		_ = c.Query("ids") // campaignIDs - will be used when implementing real API

		// TODO: Generate CSV export
		// For now, return a simple CSV

		csvData := "ID,Name,Status,Budget,Impressions,Clicks,CTR,Cost,Conversions,CPA,ROAS\n"
		csvData += "12345,Summer Sale 2024,ACTIVE,50000,1275042,15261,1.20%,1159000,412,2815,4.2\n"
		csvData += "67890,Holiday Promotion,PAUSED,75000,892156,8921,1.00%,892000,178,5011,3.1\n"

		c.Set("Content-Type", "text/csv")
		c.Set("Content-Disposition", "attachment; filename=campaigns.csv")

		return c.SendString(csvData)
	}
}

// Helper methods

func (h *campaignAPIHdl) parseFilterFromQuery(c *fiber.Ctx) *models.CampaignFilter {
	filter := &models.CampaignFilter{
		SortBy:    c.Query("sort_by", "name"),
		SortOrder: c.Query("sort_order", "asc"),
		Page:      1,
		Limit:     25,
	}

	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			filter.Page = p
		}
	}

	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil {
			filter.Limit = l
		}
	}

	return filter
}

func (h *campaignAPIHdl) parseFilterFromForm(c *fiber.Ctx) *models.CampaignFilter {
	filter := &models.CampaignFilter{
		SearchTerm: c.FormValue("search_term"),
		SortBy:     c.FormValue("sort_by", "name"),
		SortOrder:  c.FormValue("sort_order", "asc"),
		Page:       1,
		Limit:      25,
	}

	// Parse status filter
	if status := c.FormValue("status"); status != "" {
		filter.Status = []string{status}
	}

	// Parse bid strategy filter
	if bidStrategy := c.FormValue("bid_strategy"); bidStrategy != "" {
		filter.BidStrategy = []string{bidStrategy}
	}

	// Parse budget range
	if minBudget := c.FormValue("min_budget"); minBudget != "" {
		if mb, err := strconv.ParseFloat(minBudget, 64); err == nil {
			filter.MinBudget = &mb
		}
	}

	if maxBudget := c.FormValue("max_budget"); maxBudget != "" {
		if mb, err := strconv.ParseFloat(maxBudget, 64); err == nil {
			filter.MaxBudget = &mb
		}
	}

	// Parse pagination
	if page := c.FormValue("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			filter.Page = p
		}
	}

	if limit := c.FormValue("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil {
			filter.Limit = l
		}
	}

	return filter
}

func (h *campaignAPIHdl) getMockCampaignReportData(filter *models.CampaignFilter) *components.CampaignReportData {
	// Mock campaigns data - in real implementation, this would query Google Ads API
	campaigns := []*models.Campaign{
		{
			ID:                12345,
			Name:              "Summer Sale 2024",
			Status:            "ACTIVE",
			BudgetAmount:      50000,
			BidStrategy:       "TARGET_CPA",
			StartDate:         time.Now().AddDate(0, -1, 0),
			Impressions:       1275042,
			Clicks:            15261,
			CTR:               0.012,
			Cost:              1159000,
			Conversions:       412,
			CPA:               2815,
			ROAS:              4.2,
			ImpressionsChange: 12.5,
			ClicksChange:      8.3,
			CostChange:        -5.2,
			ConversionsChange: 15.7,
			CreatedAt:         time.Now().AddDate(0, -2, 0),
			UpdatedAt:         time.Now(),
		},
		{
			ID:                67890,
			Name:              "Holiday Promotion",
			Status:            "PAUSED",
			BudgetAmount:      75000,
			BidStrategy:       "TARGET_ROAS",
			StartDate:         time.Now().AddDate(0, -2, 0),
			Impressions:       892156,
			Clicks:            8921,
			CTR:               0.010,
			Cost:              892000,
			Conversions:       178,
			CPA:               5011,
			ROAS:              3.1,
			ImpressionsChange: -8.2,
			ClicksChange:      -12.1,
			CostChange:        -15.3,
			ConversionsChange: -22.4,
			CreatedAt:         time.Now().AddDate(0, -3, 0),
			UpdatedAt:         time.Now().AddDate(0, 0, -1),
		},
		{
			ID:                11111,
			Name:              "Back to School Campaign",
			Status:            "ACTIVE",
			BudgetAmount:      30000,
			BidStrategy:       "MAXIMIZE_CLICKS",
			StartDate:         time.Now().AddDate(0, 0, -15),
			Impressions:       456789,
			Clicks:            5678,
			CTR:               0.0124,
			Cost:              567800,
			Conversions:       89,
			CPA:               6380,
			ROAS:              3.8,
			ImpressionsChange: 25.3,
			ClicksChange:      18.7,
			CostChange:        12.4,
			ConversionsChange: 31.2,
			CreatedAt:         time.Now().AddDate(0, 0, -20),
			UpdatedAt:         time.Now(),
		},
	}

	// Apply filters (simplified implementation)
	filteredCampaigns := h.applyFilters(campaigns, filter)

	// Mock summary data
	summary := &models.CampaignSummary{
		TotalCampaigns:   len(filteredCampaigns),
		ActiveCampaigns:  h.countByStatus(filteredCampaigns, "ACTIVE"),
		PausedCampaigns:  h.countByStatus(filteredCampaigns, "PAUSED"),
		TotalImpressions: h.sumImpressions(filteredCampaigns),
		TotalClicks:      h.sumClicks(filteredCampaigns),
		TotalCost:        h.sumCost(filteredCampaigns),
		TotalConversions: h.sumConversions(filteredCampaigns),
		AverageCTR:       h.calculateAverageCTR(filteredCampaigns),
		AverageCPA:       h.calculateAverageCPA(filteredCampaigns),
		AverageROAS:      h.calculateAverageROAS(filteredCampaigns),
	}

	// Mock performance data
	performanceData := &models.CampaignPerformanceData{
		Labels:      []string{"Jan 1", "Jan 2", "Jan 3", "Jan 4", "Jan 5", "Jan 6", "Jan 7"},
		Impressions: []int64{180000, 195000, 210000, 185000, 220000, 205000, 190000},
		Clicks:      []int64{2100, 2300, 2500, 2200, 2600, 2400, 2200},
		Cost:        []float64{165000, 180000, 195000, 170000, 200000, 185000, 175000},
		Conversions: []int64{58, 65, 72, 61, 78, 69, 63},
	}

	return &components.CampaignReportData{
		Campaigns:       filteredCampaigns,
		Summary:         summary,
		PerformanceData: performanceData,
		Filter:          filter,
		ShowFilters:     true,
		LastUpdated:     time.Now().Format("2006-01-02 15:04:05"),
	}
}

// Filter and calculation helper methods

func (h *campaignAPIHdl) applyFilters(campaigns []*models.Campaign, filter *models.CampaignFilter) []*models.Campaign {
	filtered := campaigns

	// Apply search term filter
	if filter.SearchTerm != "" {
		var searchFiltered []*models.Campaign
		for _, campaign := range filtered {
			if contains(campaign.Name, filter.SearchTerm) {
				searchFiltered = append(searchFiltered, campaign)
			}
		}
		filtered = searchFiltered
	}

	// Apply status filter
	if len(filter.Status) > 0 {
		var statusFiltered []*models.Campaign
		for _, campaign := range filtered {
			for _, status := range filter.Status {
				if campaign.Status == status {
					statusFiltered = append(statusFiltered, campaign)
					break
				}
			}
		}
		filtered = statusFiltered
	}

	// Apply bid strategy filter
	if len(filter.BidStrategy) > 0 {
		var bidFiltered []*models.Campaign
		for _, campaign := range filtered {
			for _, strategy := range filter.BidStrategy {
				if campaign.BidStrategy == strategy {
					bidFiltered = append(bidFiltered, campaign)
					break
				}
			}
		}
		filtered = bidFiltered
	}

	// Apply budget range filter
	if filter.MinBudget != nil || filter.MaxBudget != nil {
		var budgetFiltered []*models.Campaign
		for _, campaign := range filtered {
			if filter.MinBudget != nil && campaign.BudgetAmount < *filter.MinBudget {
				continue
			}
			if filter.MaxBudget != nil && campaign.BudgetAmount > *filter.MaxBudget {
				continue
			}
			budgetFiltered = append(budgetFiltered, campaign)
		}
		filtered = budgetFiltered
	}

	return filtered
}

func (h *campaignAPIHdl) countByStatus(campaigns []*models.Campaign, status string) int {
	count := 0
	for _, campaign := range campaigns {
		if campaign.Status == status {
			count++
		}
	}
	return count
}

func (h *campaignAPIHdl) sumImpressions(campaigns []*models.Campaign) int64 {
	var total int64
	for _, campaign := range campaigns {
		total += campaign.Impressions
	}
	return total
}

func (h *campaignAPIHdl) sumClicks(campaigns []*models.Campaign) int64 {
	var total int64
	for _, campaign := range campaigns {
		total += campaign.Clicks
	}
	return total
}

func (h *campaignAPIHdl) sumCost(campaigns []*models.Campaign) float64 {
	var total float64
	for _, campaign := range campaigns {
		total += campaign.Cost
	}
	return total
}

func (h *campaignAPIHdl) sumConversions(campaigns []*models.Campaign) int64 {
	var total int64
	for _, campaign := range campaigns {
		total += campaign.Conversions
	}
	return total
}

func (h *campaignAPIHdl) calculateAverageCTR(campaigns []*models.Campaign) float64 {
	if len(campaigns) == 0 {
		return 0
	}

	totalImpressions := h.sumImpressions(campaigns)
	totalClicks := h.sumClicks(campaigns)

	if totalImpressions == 0 {
		return 0
	}

	return float64(totalClicks) / float64(totalImpressions)
}

func (h *campaignAPIHdl) calculateAverageCPA(campaigns []*models.Campaign) float64 {
	if len(campaigns) == 0 {
		return 0
	}

	totalCost := h.sumCost(campaigns)
	totalConversions := h.sumConversions(campaigns)

	if totalConversions == 0 {
		return 0
	}

	return totalCost / float64(totalConversions)
}

func (h *campaignAPIHdl) calculateAverageROAS(campaigns []*models.Campaign) float64 {
	if len(campaigns) == 0 {
		return 0
	}

	var totalROAS float64
	count := 0

	for _, campaign := range campaigns {
		if campaign.ROAS > 0 {
			totalROAS += campaign.ROAS
			count++
		}
	}

	if count == 0 {
		return 0
	}

	return totalROAS / float64(count)
}

// Helper function to check if string contains substring (case-insensitive)
func contains(str, substr string) bool {
	return len(str) >= len(substr) &&
		(str == substr ||
			(len(str) > len(substr) &&
				(str[:len(substr)] == substr ||
					str[len(str)-len(substr):] == substr ||
					containsMiddle(str, substr))))
}

func containsMiddle(str, substr string) bool {
	for i := 1; i <= len(str)-len(substr); i++ {
		if str[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
