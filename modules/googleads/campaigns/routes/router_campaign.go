package routes

import (
	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

func SetupRoutesCampaign(app *fiber.App, serviceCtx sctx.ServiceContext, store *session.Store, midds ...fiber.Handler) {

	group := app.Group("dsp/googleads/campaigns")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				group.Use(midd)
			}
		}

		comPage := ComposerCampaignService(serviceCtx)
		group.Get("/list", comPage.ListCampaignHdl(store)).Name("googleads.campaigns.list")
		group.Get("/edit", comPage.EditCampaignHdl(store)).Name("googleads.campaigns.edit")
	}
}
