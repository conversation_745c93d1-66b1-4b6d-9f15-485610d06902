package routes

import (
	"googledsp/modules/googleads/campaigns/transport/handlers"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

type ComposerCampaignHdl interface {
	ListCampaignHdl(store *session.Store) fiber.Handler
	EditCampaignHdl(store *session.Store) fiber.Handler
}

func ComposerCampaignService(serviceCtx sctx.ServiceContext) ComposerCampaignHdl {

	hdl := handlers.NewCampaignHdl()
	return hdl
}
