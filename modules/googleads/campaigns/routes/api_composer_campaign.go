package routes

import (
	"googledsp/conf"
	"googledsp/modules/googleads/campaigns/repository/mongo"
	"googledsp/modules/googleads/campaigns/transport/api"
	"googledsp/modules/googleads/campaigns/usecase"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"
	"github.com/dev-networldasia/dspgos/sctx/configs"
	"github.com/gofiber/fiber/v2"

	googleAdsReportR "googledsp/modules/googleads/adserver_report/repository/mongo"
)

type ComposerCampaignApi interface {
	ListTableCampaignsApi() fiber.Handler
}

func ComposerCampaignApiService(serviceCtx sctx.ServiceContext) ComposerCampaignApi {
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	mongoAdserverReportDB := serviceCtx.MustGet(conf.KeyCompReportMongoDB).(mongodb.MongoComponent).GetDatabase()

	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("web")

	campaignRepo := mongo.NewCampaignRepo(mongoDB)
	googleAdsReportRepo := googleAdsReportR.NewTikTokReportDetailRepo(mongoAdserverReportDB)

	param := usecase.ParamCampaignUsc{
		CampaignRepo:        campaignRepo,
		GoogleAdsReportRepo: googleAdsReportRepo,
		Logger:              logger,
	}
	usc := usecase.NewCampaignUsc(param)
	api := api.NewApiCampaign(usc)
	return api
}
