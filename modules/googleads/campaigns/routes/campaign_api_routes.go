package routes

import (
	"googledsp/modules/googleads/campaigns/transport/handlers"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/gofiber/fiber/v2"
)

type CampaignAPIComposer interface {
	GetCampaignListAPI() fiber.Handler
	FilterCampaignsAPI() fiber.Handler
	RefreshCampaignsAPI() fiber.Handler
	UpdateCampaignStatusAPI() fiber.Handler
	DuplicateCampaignAPI() fiber.Handler
	DeleteCampaignAPI() fiber.Handler
	ExportCampaignsAPI() fiber.Handler
}

func ComposerCampaignAPIService(serviceCtx sctx.ServiceContext) CampaignAPIComposer {
	hdl := handlers.NewCampaignAPIHdl()
	return hdl
}

func SetupCampaignAPIRoutes(app *fiber.App, serviceCtx sctx.ServiceContext) {
	composer := ComposerCampaignAPIService(serviceCtx)

	// API routes for campaign HTMX requests
	api := app.Group("/api/campaigns")

	// Campaign list and filtering endpoints
	api.Get("/list", composer.GetCampaignListAPI())
	api.Get("/filter", composer.FilterCampaignsAPI())
	api.Get("/refresh", composer.RefreshCampaignsAPI())

	// Campaign management endpoints
	api.Post("/:id/status", composer.UpdateCampaignStatusAPI())
	api.Post("/:id/duplicate", composer.DuplicateCampaignAPI())
	api.Delete("/:id", composer.DeleteCampaignAPI())

	// Export endpoint
	api.Get("/export", composer.ExportCampaignsAPI())
}
