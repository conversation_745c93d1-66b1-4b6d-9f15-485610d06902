package mapping

import (
	"encoding/json"
	"fmt"
	googleAdsReport "googledsp/modules/googleads/adserver_report/entity"
	"googledsp/modules/googleads/campaigns/transport/responses"
)

func MapperListTableCampaign(reportData *[]googleAdsReport.GoogleAdReportDetailEntity) *[]responses.CampaignRow {
	if reportData == nil {
		return &[]responses.CampaignRow{}
	}

	result := make([]responses.CampaignRow, 0, len(*reportData))

	for _, report := range *reportData {
		// Marshal report to JSON, then unmarshal to CampaignRow
		jsonData, _ := json.Marshal(report)

		var mapped responses.CampaignRow
		json.Unmarshal(jsonData, &mapped)

		// Tùy thuộc vào struct, bạn có thể tự tạo RowId nếu cần
		mapped.DT_RowId = fmt.Sprintf("row_%s", report.CampaignID)

		result = append(result, mapped)
	}

	return &result
}
