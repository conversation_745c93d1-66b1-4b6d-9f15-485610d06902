package composers

import (
	"googledsp/modules/googleads/transport/handlers"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/gofiber/fiber/v2"
)

type googleAdsComposer interface {
	IndexHdl() fiber.Handler
}

func ComposerGoogleAdsService(serviceCtx sctx.ServiceContext) googleAdsComposer {
	// For now, pass nil client - will use mock data
	hdl := handlers.NewDashboardHdl(nil)
	return hdl
}
