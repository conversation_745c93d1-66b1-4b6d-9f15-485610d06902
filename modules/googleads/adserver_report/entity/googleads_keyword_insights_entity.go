package entity

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

type GoogleAdKeywordInsightsEntity struct {
	ID primitive.ObjectID `bson:"_id" json:"_id"`

	// Basic identifiers
	AdvertiserID   string `bson:"advertiser_id" json:"advertiser_id"`
	AdvertiserName string `bson:"advertiser_name" json:"advertiser_name"`
	CampaignID     string `bson:"campaign_id" json:"campaign_id"`
	CampaignName   string `bson:"campaign_name" json:"campaign_name"`
	AdGroupID      string `bson:"ad_group_id" json:"ad_group_id"`
	AdGroupName    string `bson:"ad_group_name" json:"ad_group_name"`
	Date           string `bson:"date" json:"date"`

	// Keyword specific fields
	AdGroupCriterionKeywordMatchType string `bson:"ad_group_criterion_keyword_match_type" json:"ad_group_criterion_keyword_match_type"`
	AdGroupCriterionKeywordText      string `bson:"ad_group_criterion_keyword_text" json:"ad_group_criterion_keyword_text"`
	KeywordMatchTypeCategory         string `bson:"keyword_match_type_category" json:"keyword_match_type_category"`

	// Performance metrics
	AverageCPC  float64 `bson:"average_cpc" json:"average_cpc"`
	Clicks      int64   `bson:"clicks" json:"clicks"`
	Conversions float64 `bson:"conversions" json:"conversions"`
	CTR         float64 `bson:"ctr" json:"ctr"`
	Engagements int64   `bson:"engagements" json:"engagements"`
	Impressions int64   `bson:"impressions" json:"impressions"`
	Interactions int64  `bson:"interactions" json:"interactions"`

	// Search specific metrics
	SearchTopImpressionShare float64 `bson:"search_top_impression_share" json:"search_top_impression_share"`

	// Additional fields from the schema
	AdDestinationType                   string    `bson:"ad_destination_type" json:"ad_destination_type"`
	AdFormatType                        string    `bson:"ad_format_type" json:"ad_format_type"`
	AdNetworkType                       string    `bson:"ad_network_type" json:"ad_network_type"`
	ClickType                           string    `bson:"click_type" json:"click_type"`
	ConversionActionCategory            string    `bson:"conversion_action_category" json:"conversion_action_category"`
	ConversionAttributionEventType      string    `bson:"conversion_attribution_event_type" json:"conversion_attribution_event_type"`
	ConversionLagBucket                 string    `bson:"conversion_lag_bucket" json:"conversion_lag_bucket"`
	ConversionOrAdjustmentLagBucket     string    `bson:"conversion_or_adjustment_lag_bucket" json:"conversion_or_adjustment_lag_bucket"`
	ConversionValueRulePrimaryDimension string    `bson:"conversion_value_rule_primary_dimension" json:"conversion_value_rule_primary_dimension"`
	DayOfWeek                           string    `bson:"day_of_week" json:"day_of_week"`
	Device                              string    `bson:"device" json:"device"`
	ExternalConversionSource            string    `bson:"external_conversion_source" json:"external_conversion_source"`
	HistoricalCreativeQualityScore      string    `bson:"historical_creative_quality_score" json:"historical_creative_quality_score"`
	HistoricalLandingPageQualityScore   string    `bson:"historical_landing_page_quality_score" json:"historical_landing_page_quality_score"`
	HistoricalSearchPredictedCTR        string    `bson:"historical_search_predicted_ctr" json:"historical_search_predicted_ctr"`
	HotelCheckInDayOfWeek               string    `bson:"hotel_check_in_day_of_week" json:"hotel_check_in_day_of_week"`
	HotelDateSelectionType              string    `bson:"hotel_date_selection_type" json:"hotel_date_selection_type"`
	HotelPriceBucket                    string    `bson:"hotel_price_bucket" json:"hotel_price_bucket"`
	HotelRateType                       string    `bson:"hotel_rate_type" json:"hotel_rate_type"`
	InteractionEventTypes               []interface{} `bson:"interaction_event_types" json:"interaction_event_types"`
	LinkedSampleEntities                []interface{} `bson:"linked_sample_entities" json:"linked_sample_entities"`
	MonthOfYear                         string        `bson:"month_of_year" json:"month_of_year"`
	NewVersusReturningCustomers         string        `bson:"new_versus_returning_customers" json:"new_versus_returning_customers"`
	PlaceholderType                     string        `bson:"placeholder_type" json:"placeholder_type"`
	ProductChannel                      string        `bson:"product_channel" json:"product_channel"`
	ProductChannelExclusivity           string        `bson:"product_channel_exclusivity" json:"product_channel_exclusivity"`
	ProductCondition                    string        `bson:"product_condition" json:"product_condition"`
	PublisherOrganicClicks              string        `bson:"publisher_organic_clicks" json:"publisher_organic_clicks"`
	PublisherPurchasedClicks            string        `bson:"publisher_purchased_clicks" json:"publisher_purchased_clicks"`
	PublisherUnknownClicks              string        `bson:"publisher_unknown_clicks" json:"publisher_unknown_clicks"`
	RecommendationType                  string        `bson:"recommendation_type" json:"recommendation_type"`
	RunDate                             time.Time     `bson:"run_date" json:"run_date"`
	SampleBestPerformanceEntities       []interface{} `bson:"sample_best_performance_entities" json:"sample_best_performance_entities"`
	SampleGoodPerformanceEntities       []interface{} `bson:"sample_good_performance_entities" json:"sample_good_performance_entities"`
	SampleLearningPerformanceEntities   []interface{} `bson:"sample_learning_performance_entities" json:"sample_learning_performance_entities"`
	SampleLowPerformanceEntities        []interface{} `bson:"sample_low_performance_entities" json:"sample_low_performance_entities"`
	SampleUnratedPerformanceEntities    []interface{} `bson:"sample_unrated_performance_entities" json:"sample_unrated_performance_entities"`
	SearchEngineResultsPageType         string        `bson:"search_engine_results_page_type" json:"search_engine_results_page_type"`
	SearchTermMatchType                 string        `bson:"search_term_match_type" json:"search_term_match_type"`
	SKAdNetworkAdEventType              string        `bson:"sk_ad_network_ad_event_type" json:"sk_ad_network_ad_event_type"`
	SKAdNetworkAttributionCredit        string        `bson:"sk_ad_network_attribution_credit" json:"sk_ad_network_attribution_credit"`
	SKAdNetworkCoarseConversionValue    string        `bson:"sk_ad_network_coarse_conversion_value" json:"sk_ad_network_coarse_conversion_value"`
	SKAdNetworkInstalls                 string        `bson:"sk_ad_network_installs" json:"sk_ad_network_installs"`
	SKAdNetworkSourceType               string        `bson:"sk_ad_network_source_type" json:"sk_ad_network_source_type"`
	SKAdNetworkTotalConversions         string        `bson:"sk_ad_network_total_conversions" json:"sk_ad_network_total_conversions"`
	SKAdNetworkUserType                 string        `bson:"sk_ad_network_user_type" json:"sk_ad_network_user_type"`
	Slot                                string        `bson:"slot" json:"slot"`
	UpdatedBy                           string        `bson:"updated_by" json:"updated_by"`
}

func (GoogleAdKeywordInsightsEntity) CollectionName() string {
	return "googleads_report_insights_keywords"
}
