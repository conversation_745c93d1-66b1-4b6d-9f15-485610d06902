package entity

type GoogleAdReportDetailEntity struct {
	// ID                                primitive.ObjectID `bson:"_id" json:"_id"`
	AdvertiserID   string `bson:"advertiser_id" json:"advertiser_id"`
	AdvertiserName string `bson:"advertiser_name" json:"advertiser_name"`
	CampaignID     string `bson:"campaign_id" json:"campaign_id"`
	CampaignName   string `bson:"campaign_name" json:"campaign_name"`
	AdID           string `bson:"ad_id" json:"ad_id"`
	AdName         string `bson:"ad_name" json:"ad_name"`
	AdGroupID      string `bson:"ad_group_id" json:"ad_group_id"`
	AdGroupName    string `bson:"ad_group_name" json:"ad_group_name"`
	Date           string `bson:"date" json:"date"`

	ActiveViewImpressions   int64    `bson:"active_view_impressions" json:"active_view_impressions"`
	ActiveViewMeasurability float64  `bson:"active_view_measurability" json:"active_view_measurability"`
	ActiveViewViewability   float64  `bson:"active_view_viewability" json:"active_view_viewability"`
	AllConversions          float64  `bson:"all_conversions" json:"all_conversions"`
	AllConversionsValue     int64    `bson:"all_conversions_value" json:"all_conversions_value"`
	AverageCost             float64  `bson:"average_cost" json:"average_cost"`
	AverageCPC              float64  `bson:"average_cpc" json:"average_cpc"`
	AverageCPE              *float64 `bson:"average_cpe,omitempty" json:"average_cpe,omitempty"`
	AverageCPM              float64  `bson:"average_cpm" json:"average_cpm"`
	AverageCPV              *float64 `bson:"average_cpv,omitempty" json:"average_cpv,omitempty"`
	Clicks                  int64    `bson:"clicks" json:"clicks"`
	Conversions             float64  `bson:"conversions" json:"conversions"`
	CostMicros              int64    `bson:"cost_micros" json:"cost_micros"`

	Engagements   int64 `bson:"engagements" json:"engagements"`
	GmailForwards int64 `bson:"gmail_forwards" json:"gmail_forwards"`
	GmailSaves    int64 `bson:"gmail_saves" json:"gmail_saves"`

	Impressions  int64 `bson:"impressions" json:"impressions"`
	Interactions int64 `bson:"interactions" json:"interactions"`

	VideoQuartileP100Rate float64 `bson:"video_quartile_p100_rate" json:"video_quartile_p100_rate"`
	VideoQuartileP25Rate  float64 `bson:"video_quartile_p25_rate" json:"video_quartile_p25_rate"`
	VideoQuartileP50Rate  float64 `bson:"video_quartile_p50_rate" json:"video_quartile_p50_rate"`
	VideoQuartileP75Rate  float64 `bson:"video_quartile_p75_rate" json:"video_quartile_p75_rate"`
	VideoViewRate         float64 `bson:"video_view_rate" json:"video_view_rate"`
	VideoViews            int64   `bson:"video_views" json:"video_views"`

	// AdAssets                []interface{} `bson:"ad_assets" json:"ad_assets"`
	// AdAssetsResource        []interface{} `bson:"ad_assets_resource" json:"ad_assets_resource"`
	// AdDestinationType       string        `bson:"ad_destination_type" json:"ad_destination_type"`
	// AdFormatType            string        `bson:"ad_format_type" json:"ad_format_type"`
	// AdNetworkType       string  `bson:"ad_network_type" json:"ad_network_type"`
	// AdType              string  `bson:"ad_type" json:"ad_type"`
	// ClickType           string  `bson:"click_type" json:"click_type"`
	// ConversionActionCategory          string        `bson:"conversion_action_category" json:"conversion_action_category"`
	// ConversionAttributionEventType    string        `bson:"conversion_attribution_event_type" json:"conversion_attribution_event_type"`
	// ConversionLagBucket               string        `bson:"conversion_lag_bucket" json:"conversion_lag_bucket"`
	// ConversionOrAdjustmentLagBucket   string        `bson:"conversion_or_adjustment_lag_bucket" json:"conversion_or_adjustment_lag_bucket"`
	// ConversionValueRulePrimaryDim     string        `bson:"conversion_value_rule_primary_dimension" json:"conversion_value_rule_primary_dimension"`
	// Currency                          string        `bson:"currency" json:"currency"`
	// DayOfWeek                         string        `bson:"day_of_week" json:"day_of_week"`
	// Device                            string        `bson:"device" json:"device"`
	// ExternalConversionSource          string        `bson:"external_conversion_source" json:"external_conversion_source"`
	// InteractionEventTypes             []interface{} `bson:"interaction_event_types" json:"interaction_event_types"`
	// HistoricalCreativeQualityScore    string        `bson:"historical_creative_quality_score" json:"historical_creative_quality_score"`
	// HistoricalLandingPageQualityScore string        `bson:"historical_landing_page_quality_score" json:"historical_landing_page_quality_score"`
	// HistoricalSearchPredictedCTR      string        `bson:"historical_search_predicted_ctr" json:"historical_search_predicted_ctr"`
	// HotelCheckInDayOfWeek             string        `bson:"hotel_check_in_day_of_week" json:"hotel_check_in_day_of_week"`
	// HotelDateSelectionType            string        `bson:"hotel_date_selection_type" json:"hotel_date_selection_type"`
	// HotelPriceBucket                  string        `bson:"hotel_price_bucket" json:"hotel_price_bucket"`
	// HotelRateType                     string        `bson:"hotel_rate_type" json:"hotel_rate_type"`
	// LinkedSampleEntities              []interface{} `bson:"linked_sample_entities" json:"linked_sample_entities"`
	// MonthOfYear                       string        `bson:"month_of_year" json:"month_of_year"`
	// NewVersusReturningCustomers       string        `bson:"new_versus_returning_customers" json:"new_versus_returning_customers"`
	// PlaceholderType                   string        `bson:"placeholder_type" json:"placeholder_type"`
	// ProductChannel                    string        `bson:"product_channel" json:"product_channel"`
	// ProductChannelExclusivity         string        `bson:"product_channel_exclusivity" json:"product_channel_exclusivity"`
	// ProductCondition                  string        `bson:"product_condition" json:"product_condition"`
	// PublisherOrganicClicks            string        `bson:"publisher_organic_clicks" json:"publisher_organic_clicks"`
	// PublisherPurchasedClicks          string        `bson:"publisher_purchased_clicks" json:"publisher_purchased_clicks"`
	// PublisherUnknownClicks            string        `bson:"publisher_unknown_clicks" json:"publisher_unknown_clicks"`
	// RecommendationType                string        `bson:"recommendation_type" json:"recommendation_type"`
	// RunDate time.Time `bson:"run_date" json:"run_date"`
	// SampleBestPerformanceEntities     []interface{} `bson:"sample_best_performance_entities" json:"sample_best_performance_entities"`
	// SampleGoodPerformanceEntities     []interface{} `bson:"sample_good_performance_entities" json:"sample_good_performance_entities"`
	// SampleLearningPerformanceEntities []interface{} `bson:"sample_learning_performance_entities" json:"sample_learning_performance_entities"`
	// SampleLowPerformanceEntities      []interface{} `bson:"sample_low_performance_entities" json:"sample_low_performance_entities"`
	// SampleUnratedPerformanceEntities  []interface{} `bson:"sample_unrated_performance_entities" json:"sample_unrated_performance_entities"`
	// SearchEngineResultsPageType       string        `bson:"search_engine_results_page_type" json:"search_engine_results_page_type"`
	// SearchTermMatchType               string        `bson:"search_term_match_type" json:"search_term_match_type"`
	// SKAdNetworkAdEventType            string        `bson:"sk_ad_network_ad_event_type" json:"sk_ad_network_ad_event_type"`
	// SKAdNetworkAttributionCredit      string        `bson:"sk_ad_network_attribution_credit" json:"sk_ad_network_attribution_credit"`
	// SKAdNetworkCoarseConversionValue  string        `bson:"sk_ad_network_coarse_conversion_value" json:"sk_ad_network_coarse_conversion_value"`
	// SKAdNetworkInstalls               string        `bson:"sk_ad_network_installs" json:"sk_ad_network_installs"`
	// SKAdNetworkSourceType             string        `bson:"sk_ad_network_source_type" json:"sk_ad_network_source_type"`
	// SKAdNetworkTotalConversions       string        `bson:"sk_ad_network_total_conversions" json:"sk_ad_network_total_conversions"`
	// SKAdNetworkUserType               string        `bson:"sk_ad_network_user_type" json:"sk_ad_network_user_type"`
	// Slot                              string        `bson:"slot" json:"slot"`
	// UpdatedBy string `bson:"updated_by" json:"updated_by"`
}

func (GoogleAdReportDetailEntity) CollectionName() string {
	return "googleads_report_detail"
}
