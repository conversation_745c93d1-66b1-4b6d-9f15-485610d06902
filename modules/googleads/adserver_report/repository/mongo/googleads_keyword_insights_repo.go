package mongo

import (
	"context"
	"encoding/json"
	"fmt"
	"googledsp/modules/googleads/adserver_report/entity"
	googleAdsErr "googledsp/modules/googleads/common/errs"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type googleAdKeywordInsightsRepo struct {
	Collection *mongo.Collection
}

func NewGoogleAdKeywordInsightsRepo(db *mongo.Database) *googleAdKeywordInsightsRepo {
	return &googleAdKeywordInsightsRepo{
		Collection: db.Collection("googleads_report_insights_keywords"),
	}
}

/**
 * Get keyword insights with aggregation pipeline
 */
func (r *googleAdKeywordInsightsRepo) GetKeywordInsights(ctx context.Context, filter bson.M) (*[]entity.GoogleAdKeywordInsightsEntity, error) {
	// Build aggregation pipeline for keywords
	pipeline := mongo.Pipeline{
		// Match stage
		{{Key: "$match", Value: filter}},

		// Group by keyword text and ad group to aggregate metrics
		{{Key: "$group", Value: bson.M{
			"_id": bson.M{
				"keyword_text": "$ad_group_criterion_keyword_text",
				"ad_group_id":  "$ad_group_id",
				"campaign_id":  "$campaign_id",
			},
			"origin_id":                             "$first",
			"advertiser_id":                         bson.M{"$first": "$advertiser_id"},
			"advertiser_name":                       bson.M{"$first": "$advertiser_name"},
			"campaign_id":                           bson.M{"$first": "$campaign_id"},
			"campaign_name":                         bson.M{"$first": "$campaign_name"},
			"ad_group_id":                           bson.M{"$first": "$ad_group_id"},
			"ad_group_name":                         bson.M{"$first": "$ad_group_name"},
			"ad_group_criterion_keyword_text":       bson.M{"$first": "$ad_group_criterion_keyword_text"},
			"ad_group_criterion_keyword_match_type": bson.M{"$first": "$ad_group_criterion_keyword_match_type"},
			"keyword_match_type_category":           bson.M{"$first": "$keyword_match_type_category"},
			"date":                                  bson.M{"$first": "$date"},

			// Sum metrics
			"clicks":       bson.M{"$sum": "$clicks"},
			"impressions":  bson.M{"$sum": "$impressions"},
			"conversions":  bson.M{"$sum": "$conversions"},
			"engagements":  bson.M{"$sum": "$engagements"},
			"interactions": bson.M{"$sum": "$interactions"},

			// Average metrics
			"average_cpc":                 bson.M{"$avg": "$average_cpc"},
			"ctr":                         bson.M{"$avg": "$ctr"},
			"search_top_impression_share": bson.M{"$avg": "$search_top_impression_share"},
		}}},

		// Project final structure
		{{Key: "$project", Value: bson.M{
			"_id":                                   "$origin_id",
			"advertiser_id":                         1,
			"advertiser_name":                       1,
			"campaign_id":                           1,
			"campaign_name":                         1,
			"ad_group_id":                           1,
			"ad_group_name":                         1,
			"ad_group_criterion_keyword_text":       1,
			"ad_group_criterion_keyword_match_type": 1,
			"keyword_match_type_category":           1,
			"date":                                  1,
			"clicks":                                1,
			"impressions":                           1,
			"conversions":                           1,
			"engagements":                           1,
			"interactions":                          1,
			"average_cpc":                           1,
			"ctr":                                   1,
			"search_top_impression_share":           1,
		}}},

		// Sort by impressions descending
		{{Key: "$sort", Value: bson.M{"impressions": -1}}},
	}

	// Debug pipeline
	prettyJSON, _ := json.MarshalIndent(pipeline, "", "  ")
	fmt.Printf("\n===== Keyword Pipeline Debug =====\n%s\n", string(prettyJSON))

	allowDiskUse := true
	aggregateOptions := options.Aggregate().SetAllowDiskUse(allowDiskUse)
	cursor, err := r.Collection.Aggregate(ctx, pipeline, aggregateOptions)
	if err != nil {
		fmt.Printf("Aggregation error: %v\n", err)
		return nil, err
	}

	defer cursor.Close(ctx)
	if cursor == nil {
		return nil, googleAdsErr.ErrNilCursorValue
	}

	var keywords []entity.GoogleAdKeywordInsightsEntity
	err = cursor.All(ctx, &keywords)
	if err != nil {
		fmt.Printf("Error reading cursor: %v\n", err)
		return nil, err
	}

	fmt.Printf("Found %d keywords\n", len(keywords))
	return &keywords, nil
}

/**
 * Get total count of keywords
 */
func (r *googleAdKeywordInsightsRepo) GetKeywordCount(ctx context.Context, filter bson.M) (int64, error) {
	count, err := r.Collection.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}
	return count, nil
}
