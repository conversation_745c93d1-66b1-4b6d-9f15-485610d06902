package mongo

import (
	"context"
	"encoding/json"
	"fmt"
	"googledsp/modules/googleads/adserver_report/common/pipelines"
	"googledsp/modules/googleads/adserver_report/entity"
	googleAdsErr "googledsp/modules/googleads/common/errs"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type googleAdReportDetailRepo struct {
	DB         *mongo.Database
	Collection *mongo.Collection
}

func NewTikTokReportDetailRepo(DB *mongo.Database) *googleAdReportDetailRepo {
	return &googleAdReportDetailRepo{
		DB:         DB,
		Collection: DB.Collection(entity.GoogleAdReportDetailEntity{}.CollectionName()),
	}
}

/**
 * Get report detail by ad campaign
 */
func (r *googleAdReportDetailRepo) GetReportDetailByAdCampaign(ctx context.Context, filter bson.M) (*[]entity.GoogleAdReportDetailEntity, error) {
	pipeline := pipelines.GetReportCampaignPipe(filter)

	var prettyDocs []bson.M
	for _, doc := range pipeline {
		bsonDoc, err := bson.Marshal(doc)
		if err != nil {
			panic(err)
		}

		var prettyDoc bson.M
		err = bson.Unmarshal(bsonDoc, &prettyDoc)
		if err != nil {
			panic(err)
		}

		prettyDocs = append(prettyDocs, prettyDoc)
	}

	// prettyJSON, err := json.MarshalIndent(prettyDocs, "", "  ")
	prettyJSON, err := json.Marshal(prettyDocs)
	if err != nil {
		panic(err)
	}

	fmt.Println("\n \n \n xxxx-------helo------>Debug pipeline filter: %s \n \n \n ", string(prettyJSON))

	allowDiskUse := true
	aggregateOptions := options.Aggregate().SetAllowDiskUse(allowDiskUse)
	cursor, err := r.Collection.Aggregate(ctx, pipeline, aggregateOptions)
	if err != nil {
		return nil, err
	}

	defer cursor.Close(ctx)
	if cursor == nil {
		return nil, googleAdsErr.ErrNilCursorValue
	}

	var reports []entity.GoogleAdReportDetailEntity

	err = cursor.All(ctx, &reports)
	if err != nil {
		fmt.Println("Error reading cursor:", err)
		return nil, err
	}
	return &reports, nil
}

// /**
//  * Get report detail by ad adgroup
//  */
// func (r *googleAdReportDetailRepo) GetReportDetailByAdgroup(ctx context.Context, filter bson.M) (*[]entity.GoogleAdReportDetailEntity, error) {
// 	pipeline := pipelines.GetReportAdgroupPipe(filter)

// 	// var prettyDocs []bson.M
// 	// for _, doc := range pipeline {
// 	// 	bsonDoc, err := bson.Marshal(doc)
// 	// 	if err != nil {
// 	// 		panic(err)
// 	// 	}

// 	// 	var prettyDoc bson.M
// 	// 	err = bson.Unmarshal(bsonDoc, &prettyDoc)
// 	// 	if err != nil {
// 	// 		panic(err)
// 	// 	}

// 	// 	prettyDocs = append(prettyDocs, prettyDoc)
// 	// }

// 	// // prettyJSON, err := json.MarshalIndent(prettyDocs, "", "  ")
// 	// prettyJSON, err := json.Marshal(prettyDocs)
// 	// if err != nil {
// 	// 	panic(err)
// 	// }

// 	// fmt.Println("\n \n \n xxxx-------helo------>Debug pipeline filter: %s \n \n \n ", string(prettyJSON))

// 	allowDiskUse := true
// 	aggregateOptions := options.Aggregate().SetAllowDiskUse(allowDiskUse)
// 	cursor, err := r.Collection.Aggregate(ctx, pipeline, aggregateOptions)
// 	if err != nil {
// 		return nil, err
// 	}

// 	defer cursor.Close(ctx)
// 	if cursor == nil {
// 		return nil, fberrs.ErrNilCursorValue
// 	}

// 	var reports []entity.GoogleAdReportDetailEntity

// 	err = cursor.All(ctx, &reports)
// 	if err != nil {
// 		fmt.Println("Error reading cursor:", err)
// 		return nil, err
// 	}
// 	return &reports, nil
// }

// /**
//  * Get report detail by ad
//  */
// func (r *googleAdReportDetailRepo) GetReportDetailByAd(ctx context.Context, filter bson.M) (*[]entity.GoogleAdReportDetailEntity, error) {
// 	pipeline := pipelines.GetReportAdPipe(filter)

// 	// var prettyDocs []bson.M
// 	// for _, doc := range pipeline {
// 	// 	bsonDoc, err := bson.Marshal(doc)
// 	// 	if err != nil {
// 	// 		panic(err)
// 	// 	}

// 	// 	var prettyDoc bson.M
// 	// 	err = bson.Unmarshal(bsonDoc, &prettyDoc)
// 	// 	if err != nil {
// 	// 		panic(err)
// 	// 	}

// 	// 	prettyDocs = append(prettyDocs, prettyDoc)
// 	// }

// 	// // prettyJSON, err := json.MarshalIndent(prettyDocs, "", "  ")
// 	// prettyJSON, err := json.Marshal(prettyDocs)
// 	// if err != nil {
// 	// 	panic(err)
// 	// }

// 	// fmt.Println("\n \n \n xxxx-------helo------>Debug pipeline filter: %s \n \n \n ", string(prettyJSON))

// 	allowDiskUse := true
// 	aggregateOptions := options.Aggregate().SetAllowDiskUse(allowDiskUse)
// 	cursor, err := r.Collection.Aggregate(ctx, pipeline, aggregateOptions)
// 	if err != nil {
// 		return nil, err
// 	}

// 	defer cursor.Close(ctx)
// 	if cursor == nil {
// 		return nil, googleAdsErr.ErrNilCursorValue
// 	}

// 	var reports []entity.GoogleAdReportDetailEntity

// 	err = cursor.All(ctx, &reports)
// 	if err != nil {
// 		fmt.Println("Error reading cursor:", err)
// 		return nil, err
// 	}
// 	return &reports, nil
// }
