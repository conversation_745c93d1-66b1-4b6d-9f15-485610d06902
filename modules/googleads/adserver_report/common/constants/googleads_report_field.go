package constants

type <PERSON>Field struct {
	Key         string    `json:"key"`
	Title       string    `json:"title"`
	Type        string    `json:"type"`
	IDHtmlTempl string    `json:"id_html_templ,omitempty"`
	Level       *[]string `json:"level,omitempty"`
	IsReport    bool      `json:"is_report,omitempty"`
}

// var AdserverReportFields = []ReportField{
// 	{Key: "budget", Title: "Budget", Type: "float64", Level: &[]string{"CAMPAIGN", "ADGROUP"}, IDHtmlTempl: "bugetCellTable", IsReport: false},
// 	{Key: "scheduling", Title: "Ad scheduling", Type: "object", Level: &[]string{"ADGROUP"}, IDHtmlTempl: "schedulingCellTable", IsReport: false},

// 	{Key: "ad_id", Title: "Ad Id", Type: "string", IsReport: false},
// 	{Key: "ad_group_id", Title: "Adgroup Id", Type: "string", IsReport: false},
// 	{Key: "advertiser_id", Title: "Advertiser Id", Type: "string", IsReport: false},
// 	{Key: "campaign_id", Title: "Campaign Id", Type: "string", IsReport: false},
// 	{Key: "date", Title: "Date", Type: "string", IsReport: false},
// 	{Key: "ad_name", Title: "Ad Name", Type: "string", IsReport: false},
// 	{Key: "ad_group_name", Title: "Adgroup Name", Type: "string", IsReport: false},
// 	{Key: "currency", Title: "Currency", Type: "string", IsReport: false},
// 	{Key: "advertiser_name", Title: "Advertiser Name", Type: "string", IsReport: false},
// 	{Key: "campaign_name", Title: "Campaign Name", Type: "string", IsReport: false},
// 	{Key: "updated_by", Title: "Updated By", Type: "string", IsReport: false},

// 	{Key: "active_view_impressions", Title: "Active View Impressions", Type: "int32", IsReport: true},
// 	{Key: "active_view_measurability", Title: "Active View Measurability", Type: "float64", IsReport: true},
// 	{Key: "active_view_viewability", Title: "Active View Viewability", Type: "float64", IsReport: true},
// 	// {Key: "ad_destination_type", Title: "Ad Destination Type", Type: "string", IsReport: true},
// 	// {Key: "ad_format_type", Title: "Ad Format Type", Type: "string", IsReport: true},
// 	// {Key: "ad_network_type", Title: "Ad Network Type", Type: "string", IsReport: true},
// 	{Key: "all_conversions", Title: "All Conversions", Type: "int32", IsReport: true},
// 	{Key: "all_conversions_value", Title: "All Conversions Value", Type: "int32", IsReport: true},
// 	{Key: "average_cost", Title: "Average Cost", Type: "float64", IsReport: true},
// 	{Key: "average_cpc", Title: "Average CPC", Type: "float64", IsReport: true},
// 	{Key: "average_cpm", Title: "Average CPM", Type: "float64", IsReport: true},
// 	{Key: "average_cpe", Title: "Average CPE", Type: "float64", IsReport: true},
// 	{Key: "average_cpv", Title: "Average CPV", Type: "float64", IsReport: true},
// 	// {Key: "click_type", Title: "Click Type", Type: "string", IsReport: true},
// 	{Key: "clicks", Title: "Clicks", Type: "int32", IsReport: true},
// 	// {Key: "conversion_action_category", Title: "Conversion Action Category", Type: "string", IsReport: true},
// 	// {Key: "conversion_attribution_event_type", Title: "Conversion Attribution Event Type", Type: "string", IsReport: true},
// 	// {Key: "conversion_lag_bucket", Title: "Conversion Lag Bucket", Type: "string", IsReport: true},
// 	// {Key: "conversion_or_adjustment_lag_bucket", Title: "Conversion or Adjustment Lag Bucket", Type: "string", IsReport: true},
// 	// {Key: "conversion_value_rule_primary_dimension", Title: "Conversion Value Rule Primary Dimension", Type: "string", IsReport: true},
// 	{Key: "conversions", Title: "Conversions", Type: "int32", IsReport: true},
// 	{Key: "cost_micros", Title: "Cost Micros", Type: "int64", IsReport: true},
// 	// {Key: "day_of_week", Title: "Day of Week", Type: "string", IsReport: true},
// 	{Key: "device", Title: "Device", Type: "string", IsReport: true},
// 	{Key: "engagements", Title: "Engagements", Type: "int32", IsReport: true},
// 	// {Key: "external_conversion_source", Title: "External Conversion Source", Type: "string", IsReport: true},
// 	{Key: "gmail_forwards", Title: "Gmail Forwards", Type: "int32", IsReport: true},
// 	{Key: "gmail_saves", Title: "Gmail Saves", Type: "int32", IsReport: true},
// 	// {Key: "historical_creative_quality_score", Title: "Historical Creative Quality Score", Type: "string", IsReport: true},
// 	// {Key: "historical_landing_page_quality_score", Title: "Historical Landing Page Quality Score", Type: "string", IsReport: true},
// 	// {Key: "historical_search_predicted_ctr", Title: "Historical Search Predicted CTR", Type: "string", IsReport: true},
// 	// {Key: "hotel_check_in_day_of_week", Title: "Hotel Check In Day of Week", Type: "string", IsReport: true},
// 	// {Key: "hotel_date_selection_type", Title: "Hotel Date Selection Type", Type: "string", IsReport: true},
// 	// {Key: "hotel_price_bucket", Title: "Hotel Price Bucket", Type: "string", IsReport: true},
// 	// {Key: "hotel_rate_type", Title: "Hotel Rate Type", Type: "string", IsReport: true},
// 	{Key: "impressions", Title: "Impressions", Type: "int32", IsReport: true},
// 	// {Key: "interaction_event_types", Title: "Interaction Event Types", Type: "array", IsReport: true},
// 	{Key: "interactions", Title: "Interactions", Type: "int32", IsReport: true},
// 	// {Key: "linked_sample_entities", Title: "Linked Sample Entities", Type: "array", IsReport: true},
// 	// {Key: "month_of_year", Title: "Month of Year", Type: "string", IsReport: true},
// 	// {Key: "new_versus_returning_customers", Title: "New Versus Returning Customers", Type: "string", IsReport: true},
// 	// {Key: "placeholder_type", Title: "Placeholder Type", Type: "string", IsReport: true},
// 	// {Key: "product_channel", Title: "Product Channel", Type: "string", IsReport: true},
// 	// {Key: "product_channel_exclusivity", Title: "Product Channel Exclusivity", Type: "string", IsReport: true},
// 	// {Key: "product_condition", Title: "Product Condition", Type: "string", IsReport: true},
// 	// {Key: "publisher_organic_clicks", Title: "Publisher Organic Clicks", Type: "string", IsReport: true},
// 	// {Key: "publisher_purchased_clicks", Title: "Publisher Purchased Clicks", Type: "string", IsReport: true},
// 	// {Key: "publisher_unknown_clicks", Title: "Publisher Unknown Clicks", Type: "string", IsReport: true},
// 	// {Key: "recommendation_type", Title: "Recommendation Type", Type: "string", IsReport: true},
// 	{Key: "run_date", Title: "Run Date", Type: "isodate", IsReport: true},
// 	// {Key: "sample_best_performance_entities", Title: "Sample Best Performance Entities", Type: "array", IsReport: true},
// 	// {Key: "sample_good_performance_entities", Title: "Sample Good Performance Entities", Type: "array", IsReport: true},
// 	// {Key: "sample_learning_performance_entities", Title: "Sample Learning Performance Entities", Type: "array", IsReport: true},
// 	// {Key: "sample_low_performance_entities", Title: "Sample Low Performance Entities", Type: "array", IsReport: true},
// 	// {Key: "sample_unrated_performance_entities", Title: "Sample Unrated Performance Entities", Type: "array", IsReport: true},
// 	// {Key: "search_engine_results_page_type", Title: "Search Engine Results Page Type", Type: "string", IsReport: true},
// 	// {Key: "search_term_match_type", Title: "Search Term Match Type", Type: "string", IsReport: true},
// 	// {Key: "sk_ad_network_ad_event_type", Title: "SK Ad Network Ad Event Type", Type: "string", IsReport: true},
// 	// {Key: "sk_ad_network_attribution_credit", Title: "SK Ad Network Attribution Credit", Type: "string", IsReport: true},
// 	// {Key: "sk_ad_network_coarse_conversion_value", Title: "SK Ad Network Coarse Conversion Value", Type: "string", IsReport: true},
// 	// {Key: "sk_ad_network_installs", Title: "SK Ad Network Installs", Type: "string", IsReport: true},
// 	// {Key: "sk_ad_network_source_type", Title: "SK Ad Network Source Type", Type: "string", IsReport: true},
// 	// {Key: "sk_ad_network_total_conversions", Title: "SK Ad Network Total Conversions", Type: "string", IsReport: true},
// 	// {Key: "sk_ad_network_user_type", Title: "SK Ad Network User Type", Type: "string", IsReport: true},
// 	// {Key: "slot", Title: "Slot", Type: "string", IsReport: true},
// 	{Key: "video_quartile_p100_rate", Title: "Video Quartile P100 Rate", Type: "int32", IsReport: true},
// 	{Key: "video_quartile_p25_rate", Title: "Video Quartile P25 Rate", Type: "int32", IsReport: true},
// 	{Key: "video_quartile_p50_rate", Title: "Video Quartile P50 Rate", Type: "int32", IsReport: true},
// 	{Key: "video_quartile_p75_rate", Title: "Video Quartile P75 Rate", Type: "int32", IsReport: true},
// 	{Key: "video_views", Title: "Video Views", Type: "int32", IsReport: true},
// 	// {Key: "ad_assets", Title: "Ad Assets", Type: "array", IsReport: true},
// 	// {Key: "ad_assets_resource", Title: "Ad Assets Resource", Type: "array", IsReport: true},
// 	// {Key: "ad_type", Title: "Ad Type", Type: "string", IsReport: true},
// }
