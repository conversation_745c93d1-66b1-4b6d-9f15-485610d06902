package pipelines

import (
	"googledsp/modules/googleads/adserver_report/entity"
	"reflect"
	"strings"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

var FIELDS_MAX = []string{}

var FIELDS_FIRST = []string{
	// "_id",
	"campaign_id",
	"ad_id",
	"ad_group_id",
	"ad_group_name",
	"advertiser_id",
	"advertiser_name",
	"currency",
	"campaign_id",
	"campaign_name",
	"ad_name",
	"date",
	// "run_date",
	// "stat_time_day",
	"updated_by",
}

func GetReportCampaignPipe(filter interface{}) mongo.Pipeline {
	project, fields := BuildProjectFromStruct()
	fieldsMax := FIELDS_MAX
	fieldsFirst := FIELDS_FIRST

	fieldsSum := ExculudeKeys(ExculudeKeys(fields, fieldsFirst), fieldsMax)
	fieldsSum = ExculudeKeys(fieldsSum, []string{"_id"})

	// Step: $group
	groupStage1 := bson.M{
		"_id":       "$campaign_id",
		"origin_id": bson.D{{Key: "$first", Value: "$_id"}},
	}

	for _, key := range fieldsFirst {
		groupStage1[key] = bson.D{{Key: "$first", Value: "$" + key}}
	}

	for _, key := range fieldsSum {
		groupStage1[key] = bson.D{{Key: "$sum", Value: "$" + key}}
	}

	for _, key := range fieldsMax {
		groupStage1[key] = bson.D{{Key: "$max", Value: "$" + key}}
	}

	// Step: $addFields to convert "-" to 0
	sanitizeFields := bson.M{}
	for _, key := range fields {
		sanitizeFields[key] = bson.M{
			"$cond": bson.M{
				"if":   bson.M{"$eq": []interface{}{"$" + key, "-"}},
				"then": 0,
				"else": "$" + key,
			},
		}
	}

	// Final pipeline
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$group", Value: groupStage1}},
		{{Key: "$addFields", Value: sanitizeFields}}, 
		{{Key: "$project", Value: project}},
	}

	return pipeline
}

func BuildProjectFromStruct() (bson.M, []string) {
	t := reflect.TypeOf(entity.GoogleAdReportDetailEntity{})
	project := bson.M{}
	fields := []string{}

	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)

		if field.PkgPath != "" {
			continue
		}

		tag := field.Tag.Get("bson")
		if tag != "" && tag != "-" {
			key := tag
			if commaIdx := strings.Index(tag, ","); commaIdx != -1 {
				key = tag[:commaIdx]
			}
			project[key] = 1
			fields = append(fields, key)
		}
	}

	delete(project, "_id")
	project["_id"] = "$origin_id"

	return project, fields
}

func ExculudeKeys(a, b []string) []string {
	exclude := make(map[string]struct{}, len(b))
	for _, key := range b {
		exclude[key] = struct{}{}
	}
	var results []string
	for _, key := range a {
		if _, found := exclude[key]; !found {
			results = append(results, key)
		}
	}
	return results
}
