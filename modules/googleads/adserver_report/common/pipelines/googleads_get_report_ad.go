package pipelines

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func GetReportAdPipe(filter interface{}) mongo.Pipeline {
	project, fields := BuildProjectFromStruct()

	fieldsMax := FIELDS_MAX
	fieldsFirst := FIELDS_FIRST
	fieldsSum := ExculudeKeys(ExculudeKeys(fields, fieldsFirst), fieldsMax)
	fieldsSum = ExculudeKeys(fieldsSum, []string{"_id"})

	// Step: $group by ad_id
	groupStage1 := bson.M{
		"_id":       "$ad_id",
		"origin_id": bson.D{{Key: "$first", Value: "$_id"}},
	}

	for _, key := range fieldsFirst {
		groupStage1[key] = bson.D{{Key: "$first", Value: "$" + key}}
	}

	for _, key := range fieldsSum {
		groupStage1[key] = bson.D{{Key: "$sum", Value: "$" + key}}
	}

	for _, key := range fieldsMax {
		groupStage1[key] = bson.D{{Key: "$max", Value: "$" + key}}
	}

	// Step: $addFields to sanitize data
	sanitizeFields := bson.M{}
	for _, key := range fields {
		sanitizeFields[key] = bson.M{
			"$cond": bson.M{
				"if":   bson.M{"$eq": []interface{}{"$" + key, "-"}},
				"then": 0,
				"else": "$" + key,
			},
		}
	}

	// Final pipeline
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$group", Value: groupStage1}},
		{{Key: "$addFields", Value: sanitizeFields}},
		{{Key: "$project", Value: project}},
	}

	return pipeline
}
