package api

import (
	"github.com/gofiber/fiber/v2"
)

func (a *ReportApi) GetContentCustomColumnTableModalApi() fiber.Handler {
	return func(c *fiber.Ctx) error {

		// data := apiTempl.CustomColumnTableTempl{
		// 	ReportFields: &constants.AdserverReportFields, // Initialize with nil or an empty slice
		// }

		// customColumnTableHtml, err := templates.RenderToString(c, apiTempl.ModalCustomColumnTableContent(data))
		// if err != nil {
		// 	return core.ReturnErrsForApi(c, err)
		// }

		// customMetricColumnTableHtml, err := templates.RenderToString(c, apiTempl.ModalCustomMetricColumnTableContent(data))
		// if err != nil {
		// 	return core.ReturnErrsForApi(c, err)
		// }

		return c.JSON(fiber.Map{
			"msg":                             "Get content custom column table modal successfully",
			"modal_custom_column_html":        nil,
			"modal_custom_metric_column_html": nil,
		})
	}
}
