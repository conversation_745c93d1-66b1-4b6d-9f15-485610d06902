package routes

import (
	"googledsp/modules/googleads/adserver_report/transport/api"
	"googledsp/modules/googleads/adserver_report/usecase"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/dev-networldasia/dspgos/sctx/configs"

	// "github.com/dev-networldasia/dspgos/sctx/component/mongodb"
	"github.com/gofiber/fiber/v2"
)

type ComposerReportApi interface {
	GetContentCustomColumnTableModalApi() fiber.Handler
	// ListCampaignApi() fiber.Handler
}

func ComposerReportApiService(serviceCtx sctx.ServiceContext) ComposerReportApi {
	// mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("web")

	// tiktokService := services.GetTiktokServices(logger)
	// tiktokReportDetailRepo := tiktokReportDetailR.NewTikTokReportDetailRepo(mongoAdserverReportDB)

	// Init api
	// repo := mongo.NewCampaignRepo(mongoDB)

	usc := usecase.NewApiReporttUsc(logger)
	api := api.NewReportApi(usc)

	return api
}
