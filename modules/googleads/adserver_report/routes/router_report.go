package routes

import (
	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

func SetupRoutesReport(app *fiber.App, serviceCtx sctx.ServiceContext, store *session.Store, midds ...fiber.Handler) {

	apiGroup := app.Group("dsp/googleads/api/report")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				apiGroup.Use(midd)
			}
		}
		compApi := ComposerReportApiService(serviceCtx)

		apiGroup.Post("/custom-column-table-modal", compApi.GetContentCustomColumnTableModalApi()).Name("googleads.report.custom-column-table-modal")
	}

}
