package handlers

import (
	"googledsp/views/ggads/dashboards"

	"github.com/dev-networldasia/dspgos/gos/templates"
	"github.com/gofiber/fiber/v2"
)

type dashboardHdl struct {
	// googleAdsClient can be added later when needed
}

func NewDashboardHdl(googleAdsClient interface{}) *dashboardHdl {
	return &dashboardHdl{
		// For now, ignore the client parameter
	}
}

/**
 * Handler dashboard with reporting components
 */
func (h *dashboardHdl) IndexHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		return templates.Render(c, dashboards.Index())
	}
}
