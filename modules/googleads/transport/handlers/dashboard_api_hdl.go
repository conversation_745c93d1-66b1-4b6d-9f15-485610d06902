package handlers

import (
	"googledsp/views/ggads/dashboards/components"
	"time"

	"github.com/dev-networldasia/dspgos/gos/templates"
	"github.com/gofiber/fiber/v2"
)

type dashboardAPIHdl struct {
	// googleAdsClient can be added later when needed
}

func NewDashboardAPIHdl(googleAdsClient interface{}) *dashboardAPIHdl {
	return &dashboardAPIHdl{
		// For now, ignore the client parameter
	}
}

// GetMetricsAPI returns metrics data for HTMX requests
func (h *dashboardAPIHdl) GetMetricsAPI() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// For now, use mock data
		metrics := h.getMockMetrics()
		return templates.Render(c, components.MetricsCardsResponse(metrics))
	}
}

// GetPerformanceChartAPI returns chart data for HTMX requests
func (h *dashboardAPIHdl) GetPerformanceChartAPI() fiber.Handler {
	return func(c *fiber.Ctx) error {
		chartData := &components.PerformanceChartData{
			Title:       "Performance Overview",
			TimeRange:   "Last 30 days",
			ShowFilters: true,
		}

		return templates.Render(c, components.PerformanceChart(chartData))
	}
}

// UpdateDateRangeAPI handles date range updates
func (h *dashboardAPIHdl) UpdateDateRangeAPI() fiber.Handler {
	return func(c *fiber.Ctx) error {
		dateRange := c.FormValue("daterange-picker")
		compare := c.FormValue("compare") == "on"

		// Parse date range and update dashboard data
		dashboardData := h.getDashboardDataForDateRange(dateRange, compare)

		return templates.Render(c, components.DashboardReport(dashboardData))
	}
}

// ToggleCompareAPI handles compare periods toggle
func (h *dashboardAPIHdl) ToggleCompareAPI() fiber.Handler {
	return func(c *fiber.Ctx) error {
		compare := c.FormValue("compare") == "on"
		dateRange := c.FormValue("daterange-picker")

		dashboardData := h.getDashboardDataForDateRange(dateRange, compare)

		return templates.Render(c, components.DashboardReport(dashboardData))
	}
}

// SetPresetAPI handles preset date range selection
func (h *dashboardAPIHdl) SetPresetAPI() fiber.Handler {
	return func(c *fiber.Ctx) error {
		preset := c.FormValue("preset")

		dashboardData := h.getDashboardDataForPreset(preset)

		return templates.Render(c, components.DashboardReport(dashboardData))
	}
}

// RefreshDashboardAPI refreshes all dashboard data
func (h *dashboardAPIHdl) RefreshDashboardAPI() fiber.Handler {
	return func(c *fiber.Ctx) error {
		dateRange := c.FormValue("daterange-picker")
		compare := c.FormValue("compare") == "on"

		// Force refresh from Google Ads API
		dashboardData := h.getDashboardDataForDateRange(dateRange, compare)
		dashboardData.LastUpdated = time.Now().Format("2006-01-02 15:04:05")

		return templates.Render(c, components.DashboardReport(dashboardData))
	}
}

// GetCampaignPerformanceAPI returns campaign performance table data
func (h *dashboardAPIHdl) GetCampaignPerformanceAPI() fiber.Handler {
	return func(c *fiber.Ctx) error {
		return templates.Render(c, components.CampaignTableRows())
	}
}

// Helper methods

func (h *dashboardAPIHdl) getMockMetrics() *components.DashboardMetrics {
	return &components.DashboardMetrics{
		Impressions: components.MetricCardData{
			Title:      "Impressions",
			Value:      "1,275,042",
			Change:     "+585,058",
			ChangeType: "positive",
			Icon:       "ri-eye-line",
			Color:      "primary",
		},
		Cost: components.MetricCardData{
			Title:      "Cost",
			Value:      "15,261,159",
			Change:     "+5,780,055",
			ChangeType: "negative",
			Icon:       "ri-money-dollar-circle-line",
			Color:      "danger",
			Prefix:     "₫",
		},
		Conversions: components.MetricCardData{
			Title:      "Conversions",
			Value:      "0.00",
			Change:     "+0.00",
			ChangeType: "neutral",
			Icon:       "ri-exchange-line",
			Color:      "warning",
		},
		Views: components.MetricCardData{
			Title:      "Views",
			Value:      "412,595",
			Change:     "+12,173",
			ChangeType: "positive",
			Icon:       "ri-bar-chart-line",
			Color:      "info",
		},
		AvgCPA: components.MetricCardData{
			Title:      "Avg. target CPA",
			Value:      "—",
			Change:     "—",
			ChangeType: "neutral",
			Icon:       "ri-target-line",
			Color:      "warning",
		},
		AvgCPM: components.MetricCardData{
			Title:      "Avg. CPM",
			Value:      "11,969",
			Change:     "+41,772",
			ChangeType: "positive",
			Icon:       "ri-funds-line",
			Color:      "success",
			Prefix:     "₫",
		},
	}
}

func (h *dashboardAPIHdl) getDashboardDataForDateRange(dateRange string, compare bool) *components.DashboardReportData {
	// Parse date range and return appropriate data
	// This is a simplified implementation
	return &components.DashboardReportData{
		DateFilter: &components.DateFilterData{
			StartDate: "2024-01-01",
			EndDate:   "2024-01-31",
			Presets: []components.DatePreset{
				{Label: "Today", Value: "today", Days: 1},
				{Label: "Yesterday", Value: "yesterday", Days: 1},
				{Label: "Last 7 days", Value: "7d", Days: 7},
				{Label: "Last 30 days", Value: "30d", Days: 30},
			},
			ShowCompare: compare,
		},
		Metrics:     h.getMockMetrics(),
		ChartData:   &components.PerformanceChartData{Title: "Performance Overview"},
		ShowFilters: true,
		LastUpdated: time.Now().Format("2006-01-02 15:04:05"),
	}
}

func (h *dashboardAPIHdl) getDashboardDataForPreset(preset string) *components.DashboardReportData {
	// Return data based on preset selection
	return h.getDashboardDataForDateRange("", false)
}
