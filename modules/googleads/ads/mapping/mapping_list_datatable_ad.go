package mapping

import (
	"encoding/json"
	"fmt"
	googleAdsReport "googledsp/modules/googleads/adserver_report/entity"
	"googledsp/modules/googleads/ads/transport/responses"
)

func MapperListTableAd(reportData *[]googleAdsReport.GoogleAdReportDetailEntity) *[]responses.AdRow {
	if reportData == nil {
		return &[]responses.AdRow{}
	}

	result := make([]responses.AdRow, 0, len(*reportData))

	for _, report := range *reportData {
		// Marshal report to JSON, then unmarshal to AdRow
		jsonData, _ := json.Marshal(report)

		var mapped responses.AdRow
		json.Unmarshal(jsonData, &mapped)

		// Map specific fields that might have different names
		mapped.DT_RowId = fmt.Sprintf("ad_%s", report.AdID)
		mapped.AdID = report.AdID
		mapped.AdName = report.AdName
		mapped.AdGroupID = report.AdGroupID
		mapped.AdGroupName = report.AdGroupName
		mapped.CampaignID = report.CampaignID
		mapped.CampaignName = report.CampaignName
		mapped.AdvertiserID = report.AdvertiserID
		mapped.AdvertiserName = report.AdvertiserName
		mapped.CostMicro = report.CostMicros

		// Set default values for Ad specific fields if not available
		if mapped.Status == "" {
			mapped.Status = "ENABLED" // Default status
		}
		if mapped.AdType == "" {
			mapped.AdType = "RESPONSIVE_SEARCH_AD" // Default ad type
		}
		if mapped.AdStrength == "" {
			mapped.AdStrength = "GOOD" // Default ad strength
		}
		if mapped.PolicySummary == "" {
			mapped.PolicySummary = "APPROVED" // Default policy status
		}
		if mapped.Currency == "" {
			mapped.Currency = "VND" // Default currency
		}

		// Set default headlines and descriptions if not available
		if mapped.Headline1 == "" {
			mapped.Headline1 = mapped.AdName // Use ad name as headline if not available
		}
		if mapped.Description1 == "" {
			mapped.Description1 = "Quality ads for your business" // Default description
		}
		if mapped.DisplayURL == "" && mapped.FinalURL != "" {
			// Extract domain from final URL for display URL
			mapped.DisplayURL = "ads.example.com"
		}

		result = append(result, mapped)
	}

	return &result
}
