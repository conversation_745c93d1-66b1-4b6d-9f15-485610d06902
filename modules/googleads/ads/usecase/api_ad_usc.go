package usecase

import (
	"context"
	"fmt"
	"googledsp/modules/googleads/ads/transport/requests"
	"googledsp/modules/googleads/ads/transport/responses"
)

type AdUsc struct {
	// Add repository dependencies here when available
}

func NewAdUsc() *AdUsc {
	return &AdUsc{}
}

func (usc *AdUsc) ListTableAds(ctx context.Context, req *requests.ListTableAdReq) (*[]responses.AdRow, int64, error) {
	// For now, return mock data. Replace with actual database/API calls
	// TODO: Add proper logging when logger is available

	// Mock data - replace with actual data source
	mockAds := []responses.AdRow{
		{
			DT_RowId:       "ad_1",
			AdID:           "12345678901",
			AdName:         "Responsive Search Ad - Electronics Sale",
			AdGroupID:      "98765432101",
			AdGroupName:    "Search AdGroup - Electronics",
			CampaignID:     "11111111101",
			CampaignName:   "Summer Sale 2024",
			AdvertiserID:   "22222222201",
			AdvertiserName: "TechStore Vietnam",
			Status:         "ENABLED",
			AverageCost:    1250.75,
			AverageCPC:     1.25,
			AverageCPM:     15.50,
			CostMicro:      1250750000,
			Currency:       "VND",
			Impressions:    85000,
			Clicks:         1020,
			Conversions:    28,
			AdType:         "RESPONSIVE_SEARCH_AD",
			Headline1:      "Best Electronics Deals",
			Headline2:      "Summer Sale 2024",
			Headline3:      "Up to 50% Off",
			Description1:   "Shop the latest electronics with amazing discounts",
			Description2:   "Free shipping on orders over 500k VND",
			DisplayURL:     "techstore.vn/sale",
			FinalURL:       "https://techstore.vn/summer-sale-2024",
			AdStrength:     "EXCELLENT",
			PolicySummary:  "APPROVED",
		},
		{
			DT_RowId:       "ad_2",
			AdID:           "12345678902",
			AdName:         "Display Image Ad - Fashion Collection",
			AdGroupID:      "98765432102",
			AdGroupName:    "Display AdGroup - Fashion",
			CampaignID:     "11111111102",
			CampaignName:   "Holiday Promotion",
			AdvertiserID:   "22222222202",
			AdvertiserName: "Fashion Hub",
			Status:         "PAUSED",
			AverageCost:    890.25,
			AverageCPC:     0.89,
			AverageCPM:     12.30,
			CostMicro:      890250000,
			Currency:       "VND",
			Impressions:    125000,
			Clicks:         1000,
			Conversions:    15,
			AdType:         "IMAGE_AD",
			Headline1:      "New Fashion Collection",
			Description1:   "Discover the latest trends in fashion",
			DisplayURL:     "fashionhub.vn",
			FinalURL:       "https://fashionhub.vn/new-collection",
			ImageURL:       "https://fashionhub.vn/images/collection-banner.jpg",
			AdStrength:     "GOOD",
			PolicySummary:  "APPROVED",
		},
		{
			DT_RowId:       "ad_3",
			AdID:           "12345678903",
			AdName:         "Video Ad - Entertainment Content",
			AdGroupID:      "98765432103",
			AdGroupName:    "Video AdGroup - Entertainment",
			CampaignID:     "11111111103",
			CampaignName:   "Back to School Campaign",
			AdvertiserID:   "22222222203",
			AdvertiserName: "Entertainment Co",
			Status:         "ENABLED",
			AverageCost:    2100.50,
			AverageCPC:     2.10,
			AverageCPM:     25.75,
			CostMicro:      2100500000,
			Currency:       "VND",
			Impressions:    95000,
			Clicks:         1000,
			Conversions:    42,
			AdType:         "VIDEO_AD",
			Headline1:      "Back to School Entertainment",
			Description1:   "Best shows and movies for students",
			DisplayURL:     "entertainment.vn",
			FinalURL:       "https://entertainment.vn/back-to-school",
			VideoURL:       "https://entertainment.vn/videos/back-to-school-promo.mp4",
			VideoViews:     8500,
			VideoViewRate:  7200,
			AdStrength:     "GOOD",
			PolicySummary:  "APPROVED",
		},
		{
			DT_RowId:       "ad_4",
			AdID:           "12345678904",
			AdName:         "Shopping Product Ad - Home Decor",
			AdGroupID:      "98765432104",
			AdGroupName:    "Shopping AdGroup - Home & Garden",
			CampaignID:     "11111111104",
			CampaignName:   "Spring Collection",
			AdvertiserID:   "22222222204",
			AdvertiserName: "Home Garden Store",
			Status:         "ENABLED",
			AverageCost:    1650.00,
			AverageCPC:     1.65,
			AverageCPM:     20.25,
			CostMicro:      1650000000,
			Currency:       "VND",
			Impressions:    78000,
			Clicks:         1000,
			Conversions:    35,
			AdType:         "SHOPPING_PRODUCT_AD",
			Headline1:      "Spring Home Decor",
			Description1:   "Transform your home with our spring collection",
			DisplayURL:     "homegarden.vn",
			FinalURL:       "https://homegarden.vn/spring-collection",
			ImageURL:       "https://homegarden.vn/images/spring-decor.jpg",
			AdStrength:     "AVERAGE",
			PolicySummary:  "APPROVED",
		},
		{
			DT_RowId:            "ad_5",
			AdID:                "12345678905",
			AdName:              "App Install Ad - Mobile Game",
			AdGroupID:           "98765432105",
			AdGroupName:         "App AdGroup - Mobile Games",
			CampaignID:          "11111111105",
			CampaignName:        "Mobile App Promotion",
			AdvertiserID:        "22222222205",
			AdvertiserName:      "GameDev Studio",
			Status:              "ENABLED",
			AverageCost:         3200.80,
			AverageCPC:          3.20,
			AverageCPM:          40.50,
			CostMicro:           3200800000,
			Currency:            "VND",
			Impressions:         65000,
			Clicks:              1000,
			Conversions:         85,
			AdType:              "APP_INSTALL_AD",
			Headline1:           "Download Epic Adventure Game",
			Description1:        "Join millions of players in this epic adventure",
			DisplayURL:          "gamedev.vn",
			FinalURL:            "https://play.google.com/store/apps/details?id=com.gamedev.epic",
			ImageURL:            "https://gamedev.vn/images/epic-adventure-icon.png",
			SKAdNetworkInstalls: 65,
			AdStrength:          "EXCELLENT",
			PolicySummary:       "APPROVED",
		},
		{
			DT_RowId:       "ad_6",
			AdID:           "***********",
			AdName:         "Text Ad - Local Services",
			AdGroupID:      "***********",
			AdGroupName:    "Local Services AdGroup",
			CampaignID:     "***********",
			CampaignName:   "Local Business Promotion",
			AdvertiserID:   "***********",
			AdvertiserName: "Local Services Co",
			Status:         "ENABLED",
			AverageCost:    750.25,
			AverageCPC:     0.75,
			AverageCPM:     8.50,
			CostMicro:      750250000,
			Currency:       "VND",
			Impressions:    145000,
			Clicks:         1000,
			Conversions:    18,
			AdType:         "TEXT_AD",
			Headline1:      "Professional Local Services",
			Headline2:      "Trusted by 1000+ Customers",
			Description1:   "Quality services at affordable prices",
			Description2:   "Call now for free consultation",
			DisplayURL:     "localservices.vn",
			FinalURL:       "https://localservices.vn/contact",
			AdStrength:     "GOOD",
			PolicySummary:  "APPROVED",
		},
	}

	// Apply search filter if provided
	filteredAds := mockAds
	searchValue := req.GetSearchValue()
	if searchValue != "" {
		var filtered []responses.AdRow
		for _, ad := range mockAds {
			if contains(ad.AdName, searchValue) ||
				contains(ad.AdGroupName, searchValue) ||
				contains(ad.CampaignName, searchValue) ||
				contains(ad.AdvertiserName, searchValue) ||
				contains(ad.Status, searchValue) ||
				contains(ad.AdType, searchValue) ||
				contains(ad.Headline1, searchValue) {
				filtered = append(filtered, ad)
			}
		}
		filteredAds = filtered
	}

	// Apply ad filter if provided
	if req.AdId != "" {
		var filtered []responses.AdRow
		for _, ad := range filteredAds {
			if ad.AdID == req.AdId {
				filtered = append(filtered, ad)
			}
		}
		filteredAds = filtered
	}

	// Apply adgroup filter if provided
	if req.AdGroupId != "" {
		var filtered []responses.AdRow
		for _, ad := range filteredAds {
			if ad.AdGroupID == req.AdGroupId {
				filtered = append(filtered, ad)
			}
		}
		filteredAds = filtered
	}

	// Apply campaign filter if provided
	if req.CampaignId != "" {
		var filtered []responses.AdRow
		for _, ad := range filteredAds {
			if ad.CampaignID == req.CampaignId {
				filtered = append(filtered, ad)
			}
		}
		filteredAds = filtered
	}

	// Apply advertiser filter if provided
	if req.AdvertiserID != "" {
		var filtered []responses.AdRow
		for _, ad := range filteredAds {
			if ad.AdvertiserID == req.AdvertiserID {
				filtered = append(filtered, ad)
			}
		}
		filteredAds = filtered
	}

	total := int64(len(filteredAds))

	// Apply pagination
	start := req.GetSkip()
	limit := req.GetLimit()
	end := start + limit
	if end > total {
		end = total
	}

	if start >= total {
		emptyResult := make([]responses.AdRow, 0)
		return &emptyResult, total, nil
	}

	paginatedAds := filteredAds[start:end]

	// Set DT_RowId for DataTables
	for i := range paginatedAds {
		paginatedAds[i].DT_RowId = fmt.Sprintf("ad_%s", paginatedAds[i].AdID)
	}

	return &paginatedAds, total, nil
}

// Helper function to check if string contains substring (case-insensitive)
func contains(str, substr string) bool {
	if len(substr) == 0 {
		return true
	}
	if len(str) == 0 {
		return false
	}

	// Simple case-insensitive contains check
	strLower := toLower(str)
	substrLower := toLower(substr)

	return containsSubstring(strLower, substrLower)
}

func toLower(s string) string {
	result := make([]rune, len(s))
	for i, r := range s {
		if r >= 'A' && r <= 'Z' {
			result[i] = r + 32
		} else {
			result[i] = r
		}
	}
	return string(result)
}

func containsSubstring(str, substr string) bool {
	if len(substr) > len(str) {
		return false
	}
	for i := 0; i <= len(str)-len(substr); i++ {
		if str[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
