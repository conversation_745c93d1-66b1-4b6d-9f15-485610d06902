package usecase

import (
	"context"
	"fmt"
	"googledsp/modules/googleads/ads/mapping"
	"googledsp/modules/googleads/ads/transport/requests"
	"googledsp/modules/googleads/ads/transport/responses"
	"time"

	"go.mongodb.org/mongo-driver/bson"
)

type AdUsc struct {
	adRepo              AdRepo
	googleAdsReportRepo GoogleAdsReportRepo
}

type ParamAdUsc struct {
	AdRepo              AdRepo
	GoogleAdsReportRepo GoogleAdsReportRepo
}

func NewAdUsc(param ParamAdUsc) *AdUsc {
	return &AdUsc{
		adRepo:              param.AdRepo,
		googleAdsReportRepo: param.GoogleAdsReportRepo,
	}
}

func (usc *AdUsc) getFilterAdReport(ctx context.Context, payload *requests.ListTableAdReq) bson.M {
	filter := bson.M{}

	if payload.StartTime != nil && payload.EndTime != nil {
		startTimeStr := (*payload.StartTime).Format("2006-01-02")
		endTimeStr := (*payload.EndTime).Format("2006-01-02")
		filter["date"] = bson.M{"$gte": startTimeStr, "$lte": endTimeStr}
	} else {
		yesterday := time.Now().AddDate(0, 0, -1)
		endOfYesterdayStr := yesterday.Format("2006-01-02")
		startOfYesterdayStr := yesterday.AddDate(0, 0, -30).Format("2006-01-02")
		filter["date"] = bson.M{"$gte": startOfYesterdayStr, "$lte": endOfYesterdayStr}
	}

	if payload.AdId != "" {
		filter["ad_id"] = payload.AdId
	}
	if payload.AdGroupId != "" {
		filter["ad_group_id"] = payload.AdGroupId
	}
	if payload.CampaignId != "" {
		filter["campaign_id"] = payload.CampaignId
	}
	if payload.AdvertiserID != "" {
		filter["advertiser_id"] = payload.AdvertiserID
	}

	return filter
}

func (usc *AdUsc) ListTableAds(ctx context.Context, req *requests.ListTableAdReq) (*[]responses.AdRow, int64, error) {
	reportFilter := usc.getFilterAdReport(ctx, req)
	reportData, err := usc.googleAdsReportRepo.GetReportDetailByAd(ctx, reportFilter)

	seen := make(map[string]bool)
	for _, data := range *reportData {
		if seen[data.AdID] {
			fmt.Println("Duplicate AdID:", data.AdID)
		} else {
			seen[data.AdID] = true
		}
	}

	fmt.Printf("\n===== Ads len ====== %+v ", len(*reportData))
	if err != nil {
		fmt.Printf("====== Ads err ====== %+v", err)
		return nil, 0, err
	}

	dataTableRes := mapping.MapperListTableAd(reportData)

	return dataTableRes, int64(len(*dataTableRes)), nil
}
