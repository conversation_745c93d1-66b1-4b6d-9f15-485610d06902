package api

import (
	"googledsp/modules/googleads/ads/transport/requests"
	"googledsp/modules/googleads/ads/transport/responses"
	"googledsp/modules/googleads/ads/usecase"
	"net/http"

	"github.com/gofiber/fiber/v2"
)

type ApiAd struct {
	usc usecase.AdUscInterface
}

func NewApiAd(usc usecase.AdUscInterface) *ApiAd {
	return &ApiAd{
		usc: usc,
	}
}

func (api *ApiAd) ListTableAdsApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		ctx := c.Context()

		var req requests.ListTableAdReq
		if err := c.BodyParser(&req); err != nil {
			return c.Status(http.StatusBadRequest).JSON(fiber.Map{
				"error":   "Invalid request format",
				"message": err.Error(),
			})
		}

		// Validate request
		if err := req.Validate(); err != nil {
			return c.Status(http.StatusUnprocessableEntity).JSON(fiber.Map{
				"error":   "Validation failed",
				"message": err.Error(),
			})
		}

		// Set sorting and time range
		req.SetSortField()
		req.SetSortOrder()
		if err := req.SetTimeRange(); err != nil {
			return c.Status(http.StatusBadRequest).JSON(fiber.Map{
				"error":   "Invalid time range",
				"message": err.Error(),
			})
		}

		// Get ads from use case
		ads, total, err := api.usc.ListTableAds(ctx, &req)
		if err != nil {
			// TODO: Add proper logging when logger is available
			return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
				"error":   "Internal server error",
				"message": "Failed to retrieve ads",
			})
		}

		// Prepare response
		resp := responses.DatatableAdResp{
			Draw:            req.Draw,
			Data:            ads,
			RecordsTotal:    total,
			RecordsFiltered: total,
		}

		return c.JSON(resp)
	}
}
