package handlers

import (
	adV "googledsp/views/ggads/ads"

	"github.com/dev-networldasia/dspgos/gos/templates"
	"github.com/dev-networldasia/dspgos/gos/utils"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

type adHdl struct {
}

func NewAdHdl() *adHdl {
	return &adHdl{}
}

func (h *adHdl) ListAdHdl(store *session.Store) fiber.Handler {
	return func(c *fiber.Ctx) error {
		flashMsg := utils.GetFlashMessage(c, store, "ad_list_flash_msg")
		msg := ""
		if _, ok := flashMsg.(string); ok {
			msg = flashMsg.(string)
		}
		return templates.Render(c, adV.ListTableAd(&adV.ListTableAdLayoutData{
			FlashMsg: msg,
		}))
	}
}

func (h *adHdl) EditAdHdl(store *session.Store) fiber.Handler {
	return func(c *fiber.Ctx) error {
		flashMsg := utils.GetFlashMessage(c, store, "ad_list_flash_msg")
		msg := ""
		if _, ok := flashMsg.(string); ok {
			msg = flashMsg.(string)
		}

		return templates.Render(c, adV.ListTableAd(&adV.ListTableAdLayoutData{
			FlashMsg: msg,
		}))
	}
}
