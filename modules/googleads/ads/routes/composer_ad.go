package routes

import (
	"googledsp/modules/googleads/ads/transport/handlers"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

type ComposerAdHdl interface {
	ListAdHdl(store *session.Store) fiber.Handler
	EditAdHdl(store *session.Store) fiber.Handler
}

func ComposerAdService(serviceCtx sctx.ServiceContext) ComposerAdHdl {

	hdl := handlers.NewAdHdl()
	return hdl
}
