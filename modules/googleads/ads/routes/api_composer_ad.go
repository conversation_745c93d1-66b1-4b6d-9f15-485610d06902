package routes

import (
	"googledsp/conf"
	"googledsp/modules/googleads/ads/repository/mongo"
	"googledsp/modules/googleads/ads/transport/api"
	"googledsp/modules/googleads/ads/usecase"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"
	"github.com/dev-networldasia/dspgos/sctx/configs"

	googleAdsReportR "googledsp/modules/googleads/adserver_report/repository/mongo"
)

func ComposerAdApiService(serviceCtx sctx.ServiceContext) api.AdApiInterface {
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	mongoAdserverReportDB := serviceCtx.MustGet(conf.KeyCompReportMongoDB).(mongodb.MongoComponent).GetDatabase()

	// Create repositories
	adRepo := mongo.NewAdRepo(mongoDB)
	googleAdsReportRepo := googleAdsReportR.NewTikTokReportDetailRepo(mongoAdserverReportDB)

	param := usecase.ParamAdUsc{
		AdRepo:              adRepo,
		GoogleAdsReportRepo: googleAdsReportRepo,
	}
	adUsc := usecase.NewAdUsc(param)
	adApi := api.NewApiAd(adUsc)
	return adApi
}
