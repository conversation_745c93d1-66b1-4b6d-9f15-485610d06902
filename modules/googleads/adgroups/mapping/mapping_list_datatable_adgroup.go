package mapping

import (
	"encoding/json"
	"fmt"
	googleAdsReport "googledsp/modules/googleads/adserver_report/entity"
	"googledsp/modules/googleads/adgroups/transport/responses"
)

func MapperListTableAdGroup(reportData *[]googleAdsReport.GoogleAdReportDetailEntity) *[]responses.AdGroupRow {
	if reportData == nil {
		return &[]responses.AdGroupRow{}
	}

	result := make([]responses.AdGroupRow, 0, len(*reportData))

	for _, report := range *reportData {
		// Marshal report to JSON, then unmarshal to AdGroupRow
		jsonData, _ := json.Marshal(report)

		var mapped responses.AdGroupRow
		json.Unmarshal(jsonData, &mapped)

		// Map specific fields that might have different names
		mapped.DT_RowId = fmt.Sprintf("adgroup_%s", report.AdGroupID)
		mapped.AdGroupID = report.AdGroupID
		mapped.AdGroupName = report.AdGroupName
		mapped.CampaignID = report.CampaignID
		mapped.CampaignName = report.CampaignName
		mapped.AdvertiserID = report.AdvertiserID
		mapped.AdvertiserName = report.AdvertiserName
		mapped.CostMicro = report.CostMicros

		// Set default values for AdGroup specific fields if not available
		if mapped.Status == "" {
			mapped.Status = "ENABLED" // Default status
		}
		if mapped.BidStrategy == "" {
			mapped.BidStrategy = "TARGET_CPA" // Default bid strategy
		}
		if mapped.AdGroupType == "" {
			mapped.AdGroupType = "SEARCH_STANDARD" // Default ad group type
		}
		if mapped.RotationMode == "" {
			mapped.RotationMode = "OPTIMIZE" // Default rotation mode
		}
		if mapped.Currency == "" {
			mapped.Currency = "VND" // Default currency
		}

		result = append(result, mapped)
	}

	return &result
}
