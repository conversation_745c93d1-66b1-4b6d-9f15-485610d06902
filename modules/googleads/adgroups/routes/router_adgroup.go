package routes

import (
	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

func SetupRoutesAdgroup(app *fiber.App, serviceCtx sctx.ServiceContext, store *session.Store, midds ...fiber.Handler) {

	group := app.Group("dsp/googleads/adgroups")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				group.Use(midd)
			}
		}

		comPage := ComposerAdgroupService(serviceCtx)
		group.Get("/list", comPage.ListAdgroupHdl(store)).Name("googleads.adgroups.list")
		group.Get("/edit", comPage.EditAdgroupHdl(store)).Name("googleads.adgroups.edit")
	}
}
