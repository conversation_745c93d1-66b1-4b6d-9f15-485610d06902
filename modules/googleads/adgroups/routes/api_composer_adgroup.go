package routes

import (
	"googledsp/conf"
	"googledsp/modules/googleads/adgroups/repository/mongo"
	"googledsp/modules/googleads/adgroups/transport/api"
	"googledsp/modules/googleads/adgroups/usecase"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"
	"github.com/dev-networldasia/dspgos/sctx/configs"

	googleAdsReportR "googledsp/modules/googleads/adserver_report/repository/mongo"
)

func ComposerAdGroupApiService(serviceCtx sctx.ServiceContext) api.AdGroupApiInterface {
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	mongoAdserverReportDB := serviceCtx.MustGet(conf.KeyCompReportMongoDB).(mongodb.MongoComponent).GetDatabase()

	// Create repositories
	adgroupRepo := mongo.NewAdGroupRepo(mongoDB)
	googleAdsReportRepo := googleAdsReportR.NewTikTokReportDetailRepo(mongoAdserverReportDB)

	param := usecase.ParamAdGroupUsc{
		AdGroupRepo:         adgroupRepo,
		GoogleAdsReportRepo: googleAdsReportRepo,
	}
	adgroupUsc := usecase.NewAdGroupUsc(param)
	adgroupApi := api.NewApiAdGroup(adgroupUsc)
	return adgroupApi
}
