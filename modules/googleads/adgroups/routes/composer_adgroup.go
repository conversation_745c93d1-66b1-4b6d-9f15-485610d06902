package routes

import (
	"googledsp/modules/googleads/adgroups/transport/handlers"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

type ComposerAdgroupHdl interface {
	ListAdgroupHdl(store *session.Store) fiber.Handler
	EditAdgroupHdl(store *session.Store) fiber.Handler
}

func ComposerAdgroupService(serviceCtx sctx.ServiceContext) ComposerAdgroupHdl {

	hdl := handlers.NewAdgroupHdl()
	return hdl
}
