package api

import (
	"googledsp/modules/googleads/adgroups/transport/requests"
	"googledsp/modules/googleads/adgroups/transport/responses"
	"googledsp/modules/googleads/adgroups/usecase"
	"net/http"

	"github.com/gofiber/fiber/v2"
)

type ApiAdGroup struct {
	usc usecase.AdGroupUscInterface
}

func NewApiAdGroup(usc usecase.AdGroupUscInterface) *ApiAdGroup {
	return &ApiAdGroup{
		usc: usc,
	}
}

func (api *ApiAdGroup) ListTableAdGroupsApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		ctx := c.Context()

		var req requests.ListTableAdGroupReq
		if err := c.BodyP<PERSON>er(&req); err != nil {
			return c.Status(http.StatusBadRequest).JSON(fiber.Map{
				"error":   "Invalid request format",
				"message": err.Error(),
			})
		}

		// Validate request
		if err := req.Validate(); err != nil {
			return c.Status(http.StatusUnprocessableEntity).JSON(fiber.Map{
				"error":   "Validation failed",
				"message": err.Error(),
			})
		}

		// Set sorting and time range
		req.SetSortField()
		req.SetSortOrder()
		if err := req.SetTimeRange(); err != nil {
			return c.Status(http.StatusBadRequest).JSON(fiber.Map{
				"error":   "Invalid time range",
				"message": err.Error(),
			})
		}

		// Get adgroups from use case
		adgroups, total, err := api.usc.ListTableAdGroups(ctx, &req)
		if err != nil {
			// TODO: Add proper logging when logger is available
			return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
				"error":   "Internal server error",
				"message": "Failed to retrieve adgroups",
			})
		}

		// Prepare response
		resp := responses.DatatableAdGroupResp{
			Draw:            req.Draw,
			Data:            adgroups,
			RecordsTotal:    total,
			RecordsFiltered: total,
		}

		return c.JSON(resp)
	}
}
