package requests

import (
	"googledsp/modules/googleads/adgroups/common/errs"
	"googledsp/modules/googleads/campaigns/common/consts"
	"reflect"
	"time"

	"github.com/go-playground/validator/v10"
)

type ListTableAdGroupReq struct {
	Draw        int     `json:"draw" form:"draw"`
	Length      int64   `json:"length" form:"length"`
	Start       int64   `json:"start" form:"start"`
	SearchValue *string `json:"search" form:"search"`
	Order       []Order `json:"order"`
	SortField   string  `json:"-" form:"-"`
	SortOrder   int     `json:"-" form:"-"`

	Time      []string   `json:"time,omitempty" form:"time"`
	StartTime *time.Time `json:"start_time" form:"-"`
	EndTime   *time.Time `json:"end_time" form:"-"`

	AdGroupId    string   `json:"adgroupId,omitempty" form:"adgroupId" validate:"omitempty,number,min=10,max=30"`
	CampaignIds  []string `json:"campaignIds,omitempty" form:"campaignIds" validate:"omitempty,dive,required,number,min=10,max=30"`
	AdvertiserID string   `json:"advertiser_id,omitempty" form:"advertiserId" validate:"omitempty,number,min=10,max=30"`
}

type Order struct {
	Column int    `json:"column" form:"column"`
	Dir    string `json:"dir" form:"dir"`
}

func (req *ListTableAdGroupReq) Validate() error {
	validate := validator.New()

	err := validate.Struct(req)

	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "AdvertiserID":
				return errs.ErrAdvertiserIDInvalid
			case "AdgroupID":
				return errs.ErrAdgroupIDInvalid
			case "CampaignIds":
				return errs.ErrCampaignIDInvalid
			}
		}
	}
	// if req.ClientIDStr != "" && !utils.ValidateObjectID(req.ClientIDStr) {
	// 	return errs.ErrIDClientValidate
	// }

	// if req.ClientIDStr != "" {
	// 	clientID, _ := primitive.ObjectIDFromHex(req.ClientIDStr)
	// 	req.ClientID = clientID
	// }

	if reflect.TypeOf(req.SearchValue).Kind() == reflect.String {
		return nil
	}

	// if reflect.TypeOf(req.UserId).Kind() != reflect.Slice && req.UserId != nil {
	// 	return nil
	// }

	// for _, id := range req.UserId {
	// 	if reflect.TypeOf(id).Kind() != reflect.String {
	// 		return nil
	// 	}
	// }
	// if reflect.TypeOf(req.NameUser).Kind() != reflect.Slice && req.NameUser != nil {
	// 	return nil
	// }
	// for _, name := range req.NameUser {
	// 	if reflect.TypeOf(name).Kind() != reflect.String {
	// 		return nil
	// 	}
	// }

	req.SortOrder = 1
	if req.Order == nil && len(req.Order) == 0 {
		// req.SortField = consts.CAMPAIGN_ORDER_DEFAULT
	} else {
		orderColumn := req.Order[0].Column
		if sortField, ok := consts.FieldCampaignSort[orderColumn]; !ok {
			req.SortField = consts.CAMPAIGN_ORDER_DEFAULT
		} else {
			req.SortField = sortField
		}

		if sortOrder := req.Order[0].Dir; sortOrder == "desc" {
			req.SortOrder = -1
		}

		if req.SortField == consts.FieldCampaignSort[1] {
			req.SortOrder = -1
		}
	}
	if len(req.Time) < 2 {
		return nil
	}

	startTime, err := time.Parse(time.RFC3339, req.Time[0])
	if err != nil {
		return nil
	}
	req.StartTime = &startTime

	endTime, err := time.Parse(time.RFC3339, req.Time[1])
	if err != nil {
		req.StartTime = nil
		return nil
	}
	req.EndTime = &endTime

	return nil
}
