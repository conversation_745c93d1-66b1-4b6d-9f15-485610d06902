package requests

import (
	"googledsp/modules/googleads/adgroups/common/consts"
	"reflect"
	"time"
)

type ListTableAdGroupReq struct {
	Draw        int     `json:"draw" form:"draw"`
	Length      int64   `json:"length" form:"length"`
	Start       int64   `json:"start" form:"start"`
	SearchValue *string `json:"search" form:"search"`
	Order       []Order `json:"order"`
	SortField   string  `json:"-" form:"-"`
	SortOrder   int     `json:"-" form:"-"`

	Time      []string   `json:"time,omitempty" form:"time"`
	StartTime *time.Time `json:"start_time" form:"-"`
	EndTime   *time.Time `json:"end_time" form:"-"`

	AdGroupId    string `json:"adGroupId,omitempty" form:"adGroupId" validate:"omitempty,number,min=10,max=30"`
	CampaignId   string `json:"campaignId,omitempty" form:"campaignId" validate:"omitempty,number,min=10,max=30"`
	AdvertiserID string `json:"advertiser_id,omitempty" form:"advertiserId" validate:"omitempty,number,min=10,max=30"`
}

type Order struct {
	Column int    `json:"column" form:"column"`
	Dir    string `json:"dir" form:"dir"`
}

func (req *ListTableAdGroupReq) Validate() error {
	if reflect.TypeOf(req.SearchValue).Kind() == reflect.String {
		return nil
	}

	if reflect.TypeOf(req.AdGroupId).Kind() != reflect.String && req.AdGroupId != "" {
		return consts.ErrAdGroupIdValidate
	}

	if reflect.TypeOf(req.CampaignId).Kind() != reflect.String && req.CampaignId != "" {
		return consts.ErrCampaignIdValidate
	}

	if reflect.TypeOf(req.AdvertiserID).Kind() != reflect.String && req.AdvertiserID != "" {
		return consts.ErrAdvertiserIdValidate
	}

	return nil
}

func (req *ListTableAdGroupReq) GetSortField() string {
	if len(req.Order) == 0 {
		return "adgroup_name"
	}

	switch req.Order[0].Column {
	case 0:
		return "adgroup_name"
	case 1:
		return "campaign_name"
	case 2:
		return "status"
	case 3:
		return "impressions"
	case 4:
		return "clicks"
	case 5:
		return "cost_micro"
	case 6:
		return "conversions"
	default:
		return "adgroup_name"
	}
}

func (req *ListTableAdGroupReq) GetSortOrder() int {
	if len(req.Order) == 0 {
		return 1 // ASC
	}

	if req.Order[0].Dir == "desc" {
		return -1 // DESC
	}
	return 1 // ASC
}

func (req *ListTableAdGroupReq) GetLimit() int64 {
	if req.Length <= 0 {
		return 10
	}
	if req.Length > 100 {
		return 100
	}
	return req.Length
}

func (req *ListTableAdGroupReq) GetSkip() int64 {
	if req.Start < 0 {
		return 0
	}
	return req.Start
}

func (req *ListTableAdGroupReq) GetSearchValue() string {
	if req.SearchValue == nil {
		return ""
	}
	return *req.SearchValue
}

func (req *ListTableAdGroupReq) SetSortField() {
	req.SortField = req.GetSortField()
}

func (req *ListTableAdGroupReq) SetSortOrder() {
	req.SortOrder = req.GetSortOrder()
}

func (req *ListTableAdGroupReq) SetTimeRange() error {
	if len(req.Time) != 2 {
		// Set default time range (last 30 days)
		now := time.Now()
		req.EndTime = &now
		startTime := now.AddDate(0, 0, -30)
		req.StartTime = &startTime
		return nil
	}

	startTime, err := time.Parse("2006-01-02", req.Time[0])
	if err != nil {
		return err
	}
	req.StartTime = &startTime

	endTime, err := time.Parse("2006-01-02", req.Time[1])
	if err != nil {
		return err
	}
	req.EndTime = &endTime

	return nil
}
