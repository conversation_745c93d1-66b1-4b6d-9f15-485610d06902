package handlers

import (
	adgroupV "googledsp/views/ggads/adgroups"

	"github.com/dev-networldasia/dspgos/gos/templates"
	"github.com/dev-networldasia/dspgos/gos/utils"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

type adgroupHdl struct {
}

func NewAdgroupHdl() *adgroupHdl {
	return &adgroupHdl{}
}

func (h *adgroupHdl) ListAdgroupHdl(store *session.Store) fiber.Handler {
	return func(c *fiber.Ctx) error {
		flashMsg := utils.GetFlashMessage(c, store, "adgroup_list_flash_msg")
		msg := ""
		if _, ok := flashMsg.(string); ok {
			msg = flashMsg.(string)
		}
		return templates.Render(c, adgroupV.ListTableAdgroup(&adgroupV.ListTableAdgroupLayoutData{
			FlashMsg: msg,
		}))
	}
}

func (h *adgroupHdl) EditAdgroupHdl(store *session.Store) fiber.Handler {
	return func(c *fiber.Ctx) error {
		flashMsg := utils.GetFlashMessage(c, store, "adgroup_list_flash_msg")
		msg := ""
		if _, ok := flashMsg.(string); ok {
			msg = flashMsg.(string)
		}

		return templates.Render(c, adgroupV.ListTableAdgroup(&adgroupV.ListTableAdgroupLayoutData{
			FlashMsg: msg,
		}))
	}
}
