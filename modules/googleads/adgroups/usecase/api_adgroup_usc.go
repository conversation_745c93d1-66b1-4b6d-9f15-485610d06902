package usecase

import (
	"context"
	"fmt"
	"googledsp/modules/googleads/adgroups/transport/requests"
	"googledsp/modules/googleads/adgroups/transport/responses"
)

type AdGroupUsc struct {
	// Add repository dependencies here when available
}

func NewAdGroupUsc() *AdGroupUsc {
	return &AdGroupUsc{}
}

func (usc *AdGroupUsc) ListTableAdGroups(ctx context.Context, req *requests.ListTableAdGroupReq) (*[]responses.AdGroupRow, int64, error) {
	// For now, return mock data. Replace with actual database/API calls
	// TODO: Add proper logging when logger is available

	// Mock data - replace with actual data source
	mockAdGroups := []responses.AdGroupRow{
		{
			DT_RowId:       "adgroup_1",
			AdGroupID:      "12345678901",
			AdGroupName:    "Search AdGroup - Electronics",
			CampaignID:     "98765432109",
			CampaignName:   "Summer Sale 2024",
			AdvertiserID:   "11111111111",
			AdvertiserName: "TechStore Vietnam",
			Status:         "ENABLED",
			AverageCost:    2500.50,
			AverageCPC:     1.25,
			AverageCPM:     15.75,
			CostMicro:      2500500000,
			Currency:       "VND",
			Impressions:    125000,
			Clicks:         2000,
			Conversions:    45,
			BidStrategy:    "TARGET_CPA",
			MaxCPC:         2.00,
			TargetCPA:      55.56,
			AdGroupType:    "SEARCH_STANDARD",
			RotationMode:   "OPTIMIZE",
		},
		{
			DT_RowId:       "adgroup_2",
			AdGroupID:      "12345678902",
			AdGroupName:    "Display AdGroup - Fashion",
			CampaignID:     "98765432108",
			CampaignName:   "Holiday Promotion",
			AdvertiserID:   "11111111112",
			AdvertiserName: "Fashion Hub",
			Status:         "PAUSED",
			AverageCost:    1800.25,
			AverageCPC:     0.95,
			AverageCPM:     12.30,
			CostMicro:      1800250000,
			Currency:       "VND",
			Impressions:    89000,
			Clicks:         1895,
			Conversions:    32,
			BidStrategy:    "TARGET_ROAS",
			MaxCPC:         1.50,
			TargetROAS:     4.2,
			AdGroupType:    "DISPLAY_STANDARD",
			RotationMode:   "ROTATE_INDEFINITELY",
		},
		{
			DT_RowId:       "adgroup_3",
			AdGroupID:      "12345678903",
			AdGroupName:    "Video AdGroup - Entertainment",
			CampaignID:     "98765432107",
			CampaignName:   "Back to School Campaign",
			AdvertiserID:   "11111111113",
			AdvertiserName: "Entertainment Co",
			Status:         "ENABLED",
			AverageCost:    3200.75,
			AverageCPC:     1.85,
			AverageCPM:     22.50,
			CostMicro:      3200750000,
			Currency:       "VND",
			Impressions:    156000,
			Clicks:         1730,
			Conversions:    58,
			BidStrategy:    "MAXIMIZE_CLICKS",
			MaxCPC:         2.50,
			AdGroupType:    "VIDEO_TRUE_VIEW_IN_STREAM",
			RotationMode:   "OPTIMIZE",
			VideoViews:     12500,
			VideoViewRate:  8500,
		},
		{
			DT_RowId:       "adgroup_4",
			AdGroupID:      "12345678904",
			AdGroupName:    "Shopping AdGroup - Home & Garden",
			CampaignID:     "98765432106",
			CampaignName:   "Spring Collection",
			AdvertiserID:   "11111111114",
			AdvertiserName: "Home Garden Store",
			Status:         "ENABLED",
			AverageCost:    2100.00,
			AverageCPC:     1.40,
			AverageCPM:     18.25,
			CostMicro:      2100000000,
			Currency:       "VND",
			Impressions:    98000,
			Clicks:         1500,
			Conversions:    38,
			BidStrategy:    "TARGET_CPA",
			MaxCPC:         2.25,
			TargetCPA:      55.26,
			AdGroupType:    "SHOPPING_PRODUCT_ADS",
			RotationMode:   "OPTIMIZE",
		},
		{
			DT_RowId:            "adgroup_5",
			AdGroupID:           "12345678905",
			AdGroupName:         "App AdGroup - Mobile Games",
			CampaignID:          "98765432105",
			CampaignName:        "Mobile App Promotion",
			AdvertiserID:        "11111111115",
			AdvertiserName:      "GameDev Studio",
			Status:              "ENABLED",
			AverageCost:         4500.30,
			AverageCPC:          2.25,
			AverageCPM:          35.80,
			CostMicro:           4500300000,
			Currency:            "VND",
			Impressions:         75000,
			Clicks:              2000,
			Conversions:         125,
			BidStrategy:         "TARGET_ROAS",
			MaxCPC:              3.00,
			TargetROAS:          3.5,
			AdGroupType:         "SEARCH_STANDARD",
			RotationMode:        "OPTIMIZE",
			SKAdNetworkInstalls: 85,
		},
	}

	// Apply search filter if provided
	filteredAdGroups := mockAdGroups
	searchValue := req.GetSearchValue()
	if searchValue != "" {
		var filtered []responses.AdGroupRow
		for _, adgroup := range mockAdGroups {
			if contains(adgroup.AdGroupName, searchValue) ||
				contains(adgroup.CampaignName, searchValue) ||
				contains(adgroup.AdvertiserName, searchValue) ||
				contains(adgroup.Status, searchValue) {
				filtered = append(filtered, adgroup)
			}
		}
		filteredAdGroups = filtered
	}

	// Apply campaign filter if provided
	if req.CampaignId != "" {
		var filtered []responses.AdGroupRow
		for _, adgroup := range filteredAdGroups {
			if adgroup.CampaignID == req.CampaignId {
				filtered = append(filtered, adgroup)
			}
		}
		filteredAdGroups = filtered
	}

	// Apply adgroup filter if provided
	if req.AdGroupId != "" {
		var filtered []responses.AdGroupRow
		for _, adgroup := range filteredAdGroups {
			if adgroup.AdGroupID == req.AdGroupId {
				filtered = append(filtered, adgroup)
			}
		}
		filteredAdGroups = filtered
	}

	// Apply advertiser filter if provided
	if req.AdvertiserID != "" {
		var filtered []responses.AdGroupRow
		for _, adgroup := range filteredAdGroups {
			if adgroup.AdvertiserID == req.AdvertiserID {
				filtered = append(filtered, adgroup)
			}
		}
		filteredAdGroups = filtered
	}

	total := int64(len(filteredAdGroups))

	// Apply pagination
	start := req.GetSkip()
	limit := req.GetLimit()
	end := start + limit
	if end > total {
		end = total
	}

	if start >= total {
		emptyResult := make([]responses.AdGroupRow, 0)
		return &emptyResult, total, nil
	}

	paginatedAdGroups := filteredAdGroups[start:end]

	// Set DT_RowId for DataTables
	for i := range paginatedAdGroups {
		paginatedAdGroups[i].DT_RowId = fmt.Sprintf("adgroup_%s", paginatedAdGroups[i].AdGroupID)
	}

	return &paginatedAdGroups, total, nil
}

// Helper function to check if string contains substring (case-insensitive)
func contains(str, substr string) bool {
	if len(substr) == 0 {
		return true
	}
	if len(str) == 0 {
		return false
	}

	// Simple case-insensitive contains check
	strLower := toLower(str)
	substrLower := toLower(substr)

	return containsSubstring(strLower, substrLower)
}

func toLower(s string) string {
	result := make([]rune, len(s))
	for i, r := range s {
		if r >= 'A' && r <= 'Z' {
			result[i] = r + 32
		} else {
			result[i] = r
		}
	}
	return string(result)
}

func containsSubstring(str, substr string) bool {
	if len(substr) > len(str) {
		return false
	}
	for i := 0; i <= len(str)-len(substr); i++ {
		if str[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
