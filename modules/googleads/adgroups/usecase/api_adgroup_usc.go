package usecase

import (
	"context"
	"fmt"
	"googledsp/modules/googleads/adgroups/mapping"
	"googledsp/modules/googleads/adgroups/transport/requests"
	"googledsp/modules/googleads/adgroups/transport/responses"
	"time"

	"go.mongodb.org/mongo-driver/bson"
)

type AdGroupUsc struct {
	adgroupRepo         AdGroupRepo
	googleAdsReportRepo GoogleAdsReportRepo
}

type ParamAdGroupUsc struct {
	AdGroupRepo         AdGroupRepo
	GoogleAdsReportRepo GoogleAdsReportRepo
}

func NewAdGroupUsc(param ParamAdGroupUsc) *AdGroupUsc {
	return &AdGroupUsc{
		adgroupRepo:         param.AdGroupRepo,
		googleAdsReportRepo: param.GoogleAdsReportRepo,
	}
}

func (usc *AdGroupUsc) getFilterAdGroupReport(ctx context.Context, payload *requests.ListTableAdGroupReq) bson.M {
	filter := bson.M{}

	if payload.StartTime != nil && payload.EndTime != nil {
		startTimeStr := (*payload.StartTime).Format("2006-01-02")
		endTimeStr := (*payload.EndTime).Format("2006-01-02")
		filter["date"] = bson.M{"$gte": startTimeStr, "$lte": endTimeStr}
	} else {
		yesterday := time.Now().AddDate(0, 0, -1)
		endOfYesterdayStr := yesterday.Format("2006-01-02")
		startOfYesterdayStr := yesterday.AddDate(0, 0, -30).Format("2006-01-02")
		filter["date"] = bson.M{"$gte": startOfYesterdayStr, "$lte": endOfYesterdayStr}
	}

	if payload.AdGroupId != "" {
		filter["ad_group_id"] = payload.AdGroupId
	}
	if payload.CampaignId != "" {
		filter["campaign_id"] = payload.CampaignId
	}
	if payload.AdvertiserID != "" {
		filter["advertiser_id"] = payload.AdvertiserID
	}

	return filter
}

func (usc *AdGroupUsc) ListTableAdGroups(ctx context.Context, req *requests.ListTableAdGroupReq) (*[]responses.AdGroupRow, int64, error) {
	reportFilter := usc.getFilterAdGroupReport(ctx, req)
	reportData, err := usc.googleAdsReportRepo.GetReportDetailByAdGroup(ctx, reportFilter)

	seen := make(map[string]bool)
	for _, data := range *reportData {
		if seen[data.AdGroupID] {
			fmt.Println("Duplicate AdGroupID:", data.AdGroupID)
		} else {
			seen[data.AdGroupID] = true
		}
	}

	fmt.Printf("\n===== AdGroups len ====== %+v ", len(*reportData))
	if err != nil {
		fmt.Printf("====== AdGroups err ====== %+v", err)
		return nil, 0, err
	}

	dataTableRes := mapping.MapperListTableAdGroup(reportData)

	return dataTableRes, int64(len(*dataTableRes)), nil
}
