package usecase

import (
	"context"
	"googledsp/modules/googleads/adgroups/transport/requests"
	"googledsp/modules/googleads/adgroups/transport/responses"
	"googledsp/modules/googleads/adserver_report/entity"

	"go.mongodb.org/mongo-driver/bson"
)

type AdGroupRepo interface {
}

type GoogleAdsReportRepo interface {
	GetReportDetailByAdGroup(ctx context.Context, filter bson.M) (*[]entity.GoogleAdReportDetailEntity, error)
}

type AdGroupUscInterface interface {
	ListTableAdGroups(ctx context.Context, req *requests.ListTableAdGroupReq) (*[]responses.AdGroupRow, int64, error)
}
