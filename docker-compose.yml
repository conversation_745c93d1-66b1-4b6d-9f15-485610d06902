version: "3.9"

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: googledsp-app
    ports:
      - "${FIBER_PORT:-3000}:3000"
    environment:
      - APP_ENV=${APP_ENV:-dev}
      - FIBER_PORT=3000
      - MONGODB_HOST=mongodb
      - MONGODB_PORT=27017
      - MONGODB_USER=${MONGODB_USER:-googledsp}
      - MONGODB_PASS=${MONGODB_PASS:-123456Xyz}
      - MONGODB_DATABASE=${MONGODB_DATABASE:-googledsp}
      - MONGODB_DSN=mongodb://${MONGODB_USER:-googledsp}:${MONGODB_PASS:-123456Xyz}@mongodb:27017/${MONGODB_DATABASE:-googledsp}
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_USER=${POSTGRES_USER:-googledsp}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-123456Xyz}
      - POSTGRES_DATABASE=${POSTGRES_DATABASE:-googledsp}
      - POSTGRES_DSN=postgres://${POSTGRES_USER:-googledsp}:${POSTGRES_PASSWORD:-123456Xyz}@postgres:5432/${POSTGRES_DATABASE:-googledsp}?sslmode=disable
      - REDIS_HOST=redis:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-123456Xyz@}
      - REDIS_USERNAME=${REDIS_USERNAME:-googledsp}
      - REDIS_URI=redis://${REDIS_USERNAME:-googledsp}:${REDIS_PASSWORD:-123456Xyz@}@redis:6379
      - JWT_SECRET=${JWT_SECRET:-your-secret-key-here}
      - LOG_LEVEL=${LOG_LEVEL:-info}
      # Google Ads API Configuration
      - GOOGLE_ADS_CLIENT_ID=${GOOGLE_ADS_CLIENT_ID}
      - GOOGLE_ADS_CLIENT_SECRET=${GOOGLE_ADS_CLIENT_SECRET}
      - GOOGLE_ADS_REFRESH_TOKEN=${GOOGLE_ADS_REFRESH_TOKEN}
      - GOOGLE_ADS_DEVELOPER_TOKEN=${GOOGLE_ADS_DEVELOPER_TOKEN}
      - GOOGLE_ADS_CUSTOMER_ID=${GOOGLE_ADS_CUSTOMER_ID}
      # Campaign Management
      - DEFAULT_CAMPAIGN_BUDGET=${DEFAULT_CAMPAIGN_BUDGET:-1000}
      - DEFAULT_BID_STRATEGY=${DEFAULT_BID_STRATEGY:-TARGET_CPA}
      - DEFAULT_TARGET_CPA=${DEFAULT_TARGET_CPA:-10.00}
      # Reporting
      - REPORT_TIMEZONE=${REPORT_TIMEZONE:-Asia/Ho_Chi_Minh}
      - REPORT_DATE_FORMAT=${REPORT_DATE_FORMAT:-2006-01-02}
    depends_on:
      mongodb:
        condition: service_healthy
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
    volumes:
      - ./public:/app/public
      - ./logs:/app/logs
    networks:
      - googledsp-network
    restart: unless-stopped

  mongodb:
    image: mongo:7.0
    container_name: googledsp-mongodb
    ports:
      - "${MONGODB_PORT:-27018}:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGODB_USER:-googledsp}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGODB_PASS:-123456Xyz}
      - MONGO_INITDB_DATABASE=${MONGODB_DATABASE:-googledsp}
    volumes:
      - mongodb_data:/data/db
      - ./docker/mongodb/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - googledsp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  postgres:
    image: postgres:16-alpine
    container_name: googledsp-postgres
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-googledsp}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-123456Xyz}
      - POSTGRES_DB=${POSTGRES_DATABASE:-googledsp}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init-postgres.sql:/docker-entrypoint-initdb.d/init-postgres.sql:ro
    networks:
      - googledsp-network
    restart: unless-stopped
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "pg_isready -U ${POSTGRES_USER:-googledsp} -d ${POSTGRES_DATABASE:-googledsp}",
        ]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  redis:
    image: redis:7.2-alpine
    container_name: googledsp-redis
    ports:
      - "${REDIS_PORT:-6326}:6379"
    command: redis-server --requirepass ${REDIS_PASSWORD:-123456Xyz@}
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - googledsp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Optional: MongoDB Express for database management
  mongo-express:
    image: mongo-express:1.0.2
    container_name: googledsp-mongo-express
    ports:
      - "8081:8081"
    environment:
      - ME_CONFIG_MONGODB_ADMINUSERNAME=${MONGODB_USER:-googledsp}
      - ME_CONFIG_MONGODB_ADMINPASSWORD=${MONGODB_PASS:-123456Xyz}
      - ME_CONFIG_MONGODB_URL=mongodb://${MONGODB_USER:-googledsp}:${MONGODB_PASS:-123456Xyz}@mongodb:27017/
      - ME_CONFIG_BASICAUTH_USERNAME=admin
      - ME_CONFIG_BASICAUTH_PASSWORD=admin123
    depends_on:
      - mongodb
    networks:
      - googledsp-network
    restart: unless-stopped
    profiles:
      - tools

  # Optional: Redis Commander for Redis management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: googledsp-redis-commander
    ports:
      - "8082:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379:0:${REDIS_PASSWORD:-123456Xyz@}
    depends_on:
      - redis
    networks:
      - googledsp-network
    restart: unless-stopped
    profiles:
      - tools

volumes:
  mongodb_data:
    driver: local
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  googledsp-network:
    driver: bridge
