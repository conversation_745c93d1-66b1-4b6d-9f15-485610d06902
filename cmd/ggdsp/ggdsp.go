package ggdsp

import (
	"fmt"
	"googledsp/cmd/ggdsp/internal"
	"googledsp/conf"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/dev-networldasia/dspgos/sctx/component/fiberapp"
	"github.com/dev-networldasia/dspgos/sctx/component/google/googleads"
	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"
	"github.com/dev-networldasia/dspgos/sctx/configs"
	"github.com/gofiber/fiber/v2/middleware/session"
	"github.com/spf13/cobra"
)

var (
	webGGNameServer = "google-dsp-service"
	version         = "1.0.0"
)

func newServerGoogleServiceCtx() sctx.ServiceContext {
	return sctx.NewServiceContext(
		sctx.WithName(webGGNameServer),
		sctx.WithComponent(fiberapp.NewFiber(configs.KeyCompFIBER)),
		sctx.WithComponent(mongodb.NewMongoDB(configs.KeyCompMongoDB, "")),
		sctx.WithComponent(mongodb.NewMongoDB(conf.KeyCompReportMongoDB, "report")),
		sctx.WithComponent(googleads.NewGoogleAds(configs.KeyGoogleAds)),
		sctx.WithComponent(sctx.NewAppLoggerDaily(configs.KeyLoggerDaily)),
	)
}

var (
	ServerGGDspCmd = &cobra.Command{
		Use:     "googledsp",
		Short:   "web google run godsp social",
		Long:    `web google CLI Long godsp social`,
		Version: version,
		Run: func(cmd *cobra.Command, args []string) {
			fmt.Println("--->> server run <<---")

			serviceCtx := newServerGoogleServiceCtx()
			loggerSv := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("google-dsp")
			loggerSv.Info("Web Facebook is running!")

			if err := serviceCtx.Load(); err != nil {
				loggerSv.Fatal(err)
			}

			fiberComp := serviceCtx.MustGet(configs.KeyCompFIBER).(fiberapp.FiberComponent)
			router := fiberComp.GetApp()

			// Test Google ads service context

			// developerToken := os.Getenv("GOOGLE_ADS_DEVELOPER_TOKEN")
			// serviceAccountPath := os.Getenv("GOOGLE_ADS_SERVICE_ACCOUNT")
			// customerID := os.Getenv("GOOGLE_ADS_LOGIN_CUSTOMER_ID")

			// fmt.Println("Developer Token: ", developerToken)
			// fmt.Println("Service Account Path: ", serviceAccountPath)
			// fmt.Println("Customer ID: ", customerID)

			// googleAdsComp := serviceCtx.MustGet(configs.KeyGoogleAds).(googleads.GoogleAdsServices)
			// client := googleAdsComp.GetGoogleAdsService()

			// advertiserID := "**********"
			// campaigns, err := client.ListCampaignStream(context.Background(), advertiserID, nil)
			// if err != nil {
			// 	log.Fatalf("failed to list advertiser: %v", err)
			// }

			// fmt.Printf("Campaigns: %+v - len %d\n", campaigns, len(campaigns))
			// os.Exit(1)

			internal.SetupRoutes(router, serviceCtx, session.New())

			if err := router.Listen(fmt.Sprintf(":%d", fiberComp.GetPort())); err != nil {
				loggerSv.Fatal(err)
			}
		},
	}
)
