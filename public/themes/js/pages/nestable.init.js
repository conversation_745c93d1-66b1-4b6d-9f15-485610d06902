function initSortable() {
  var nestedSortables = [].slice.call(
    document.querySelectorAll(".nested-sortable")
  );

  nestedSortables.forEach(function (el) {
    new Sortable(el, {
      group: "nested",
      animation: 150,
      fallbackOnBody: true,
      swapThreshold: 0.65,
      draggable: ".list-group-item.nested-1"
    });
  });

  var nestedSortablesHandles = [].slice.call(
    document.querySelectorAll(".nested-sortable-handle")
  );

  nestedSortablesHandles.forEach(function (el) {
    new Sortable(el, {
      handle: ".handle",
      group: "nested",
      animation: 150,
      fallbackOnBody: true,
      swapThreshold: 0.65,
    });
  });
}
export {initSortable};
