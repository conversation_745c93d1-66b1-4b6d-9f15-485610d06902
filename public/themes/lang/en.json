{"t-menu": "<PERSON><PERSON>", "t-dashboard": "Dashboard", "t-analytics": "Analytics", "t-crm": "CRM", "t-ecommerce": "Ecommerce", "t-crypto": "Crypto", "t-projects": "Projects", "t-nft": "NFT", "t-job": "Job", "t-apps": "Apps", "t-calendar": "Calendar", "t-chat": "Cha<PERSON>", "t-email": "Email", "t-mailbox": "Mailbox", "t-email-templates": "Email Templates", "t-basic-action": "Basic Action", "t-ecommerce-action": "Ecommerce Action", "t-products": "Products", "t-product-Details": "Product Details", "t-create-product": "Create Product", "t-orders": "Orders", "t-order-details": "Order Details", "t-customers": "Customers", "t-shopping-cart": "Shopping Cart", "t-checkout": "Checkout", "t-sellers": "Sellers", "t-sellers-details": "<PERSON><PERSON>", "t-list": "List", "t-overview": "Overview", "t-create-project": "Create Project", "t-tasks": "Tasks", "t-kanbanboard": "Kanban Board", "t-list-view": "List View", "t-task-details": "Task Details", "t-contacts": "Contacts", "t-companies": "Companies", "t-deals": "Deals", "t-leads": "Leads", "t-transactions": "Transactions", "t-buy-sell": "Buy & Sell", "t-my-wallet": "My Wallet", "t-ico-list": "ICO List", "t-kyc-application": "KYC Application", "t-invoices": "Invoices", "t-details": "Details", "t-create-invoice": "Create Invoice", "t-supprt-tickets": "Support Tickets", "t-ticket-details": "Ticket Details", "t-nft-marketplace": "NFT Marketplace", "t-marketplace": "Marketplace", "t-explore-now": "Explore Now", "t-live-auction": "Live Auction", "t-item-details": "<PERSON><PERSON>", "t-collections": "Collections", "t-creators": "Creators", "t-ranking": "Ranking", "t-wallet-connect": "Wallet Connect", "t-create-nft": "Create NFT", "t-file-manager": "File Manager", "t-to-do": "To Do", "t-jobs": "Jobs", "t-statistics": "Statistics", "t-job-lists": "Job Lists", "t-candidate-lists": "Candidate Lists", "t-grid-view": "Grid View", "t-application": "Application", "t-new-job": "New Job", "t-companies-list": "Companies List", "t-job-categories": "Job Categories", "t-layouts": "Layouts", "t-api-key": "API Key", "t-horizontal": "Horizontal", "t-detached": "Detached", "t-two-column": "Two Column", "t-hovered": "Hovered", "t-pages": "Pages", "t-authentication": "Accounts & Permissions", "t-signin": "Sign In", "t-basic": "Basic", "t-cover": "Cover", "t-signup": "Sign Up", "t-password-reset": "Password Reset", "t-password-create": "Password Create", "t-lock-screen": "Lock Screen", "t-logout": "Logout", "t-success-message": "Success Message", "t-two-step-verification": "Two Step Verification", "t-errors": "Errors", "t-404-basic": "404 Basic", "t-404-cover": "404 Cover", "t-404-alt": "404 Alt", "t-500": "500", "t-offline-page": "Offline Page", "t-starter": "Starter", "t-profile": "Profile", "t-simple-page": "Simple Page", "t-settings": "Settings", "t-team": "Team", "t-timeline": "Timeline", "t-faqs": "FAQs", "t-pricing": "Pricing", "t-gallery": "Gallery", "t-maintenance": "Maintenance", "t-coming-soon": "Coming Soon", "t-sitemap": "Sitemap", "t-search-results": "Search Results", "t-privacy-policy": "Privacy Policy", "t-landing": "Landing", "t-one-page": "One Page", "t-nft-landing": "NFT Landing", "t-components": "Components", "t-base-ui": "Base UI", "t-alerts": "<PERSON><PERSON><PERSON>", "t-badges": "Badges", "t-buttons": "Buttons", "t-colors": "Colors", "t-cards": "Cards", "t-carousel": "Carousel", "t-dropdowns": "Dropdowns", "t-grid": "Grid", "t-images": "Images", "t-tabs": "Tabs", "t-accordion-collapse": "Accordion & Collapse", "t-modals": "Modals", "t-offcanvas": "<PERSON><PERSON><PERSON>", "t-placeholders": "Placeholders", "t-progress": "Progress", "t-notifications": "Notifications", "t-media-object": "Media object", "t-embed-video": "Embed Video", "t-typography": "Typography", "t-lists": "Lists", "t-general": "General", "t-ribbons": "Ribbons", "t-utilities": "Utilities", "t-advance-ui": "Advance UI", "t-new": "New", "t-hot": "Hot", "t-sweet-alerts": "<PERSON> Alerts", "t-nestable-list": "Nestable List", "t-scrollbar": "Sc<PERSON><PERSON>", "t-animation": "Animation", "t-tour": "Tour", "t-swiper-slider": "<PERSON><PERSON><PERSON>r", "t-ratings": "Ratings", "t-highlight": "Highlight", "t-scrollSpy": "ScrollSpy", "t-widgets": "Widgets", "t-forms": "Forms", "t-basic-elements": "Basic Elements", "t-form-select": "Form Select", "t-checkboxs-radios": "Checkboxs & Radios", "t-pickers": "Pickers", "t-input-masks": "Input Masks", "t-advanced": "Advanced", "t-range-slider": "Range Slider", "t-validation": "Validation", "t-wizard": "<PERSON>", "t-editors": "Editors", "t-file-uploads": "File Uploads", "t-form-layouts": "Form Layouts", "t-select2": "Select2", "t-tables": "Tables", "t-basic-tables": "Basic Tables", "t-grid-js": "Grid J<PERSON>", "t-list-js": "List Js", "t-datatables": "Datatables", "t-charts": "Charts", "t-apexcharts": "Apexcharts", "t-line": "Line", "t-area": "Area", "t-column": "Column", "t-bar": "Bar", "t-mixed": "Mixed", "t-candlstick": "Candlstick", "t-boxplot": "Boxplot", "t-bubble": "Bubble", "t-scatter": "<PERSON><PERSON><PERSON>", "t-heatmap": "Heatmap", "t-treemap": "Treemap", "t-pie": "Pie", "t-radialbar": "Radialbar", "t-radar": "Radar", "t-polar-area": "Polar Area", "t-chartjs": "Chartjs", "t-echarts": "Echarts", "t-icons": "Icons", "t-remix": "Remix", "t-boxicons": "Boxicons", "t-material-design": "Material Design", "t-line-awesome": "Line Awesome", "t-feather": "<PERSON><PERSON>", "t-crypto-svg": "Crypto SVG", "t-maps": "Maps", "t-google": "Google", "t-vector": "Vector", "t-leaflet": "Leaflet", "t-multi-level": "Multi Level", "t-level-1.1": "Level 1.1", "t-level-1.2": "Level 1.2", "t-level-2.1": "Level 2.1", "t-level-2.2": "Level 2.2", "t-level-3.1": "Level 3.1", "t-level-3.2": "Level 3.2", "t-admin": "Admin", "t-add-user": "Add user", "t-add-role": "Add role", "t-fullname": "Fullname", "t-audit-info": "Audit info", "t-user-information": "User information", "t-birthday": "Birthday", "t-created-by": "Created by", "t-created-at": "Created at", "t-updated-by": "Updated by", "t-updated-at": "Updated at", "t-created": "Created", "t-updated": "Updated", "t-lastname": "Last name", "t-firstname": "First name", "t-password": "Password", "t-male": "Male", "t-female": "Female", "t-close": "Close", "t-submit-create": "Submit", "t-password-contain": "Password must contain", "t-minium": "Minium", "t-8-characters": "8 characters", "t-choose-role": "--- Choose role ---", "t-information": "Information", "t-back": "Back", "t-submit": "Submit", "t-promotion": "Promotion", "t-check-promotion": "Promotion not applicable", "t-product-price": "Product price", "t-comparative-price": "Comparative price", "t-price": "Price", "t-inventory-management": "Inventory management", "t-has-inventory-management": "Has inventory management", "t-lot-and-expiration": "Lot - Expiration", "t-cost-price": "Cost Price", "t-profit-margin": "Profit margin", "t-profit": "Profit", "t-barcode": "Barcode", "t-sku": "SKU", "t-allow-backorder": "Allow orders when out of stock", "t-available-stock": "Available Stock", "t-select-storage-warehouse": "Select Storage Warehouse", "t-complete": "Complete", "t-create": "Create", "t-seo-general": "General", "t-seo-advance": "Advance", "t-seo-schema": "<PERSON><PERSON><PERSON>", "t-seo-qa": "QA", "t-product-image": "Product Image", "t-no-index": "No index", "t-index": "Index", "t-no-follow": "No follow", "t-no-archive": "No archive", "t-no-image-index": "No Image Index", "t-no-snippet": "No Snippet", "t-canonical-url": "Canonical URL", "t-canonical-url-tip": "Canonical URL to avoid duplicate content.", "t-meta-robots": "Robots Meta", "t-index-meta": "Index meta", "t-keyword": "Key", "t-option": "Option", "t-image": "Image", "t-qa": "Q&A", "t-question": "Question", "t-answer": "Answer", "t-add-qa": "Add Q&A", "t-use": "Use", "t-auto-system": "Automatic system", "t-manual-setup": "Manual setup", "t-user-list": "User list", "t-role-list": "Role list", "search-by-status": "Search by status", "search-by-role": "Search by role", "t-deleted-status": "Deleted", "t-manual-schema-setup": "<PERSON> <PERSON><PERSON>a <PERSON>up", "t-transport": "Transport", "t-mass": "Mass", "t-mass-tooltip": "Used to calculate volume when shipping", "t-store": "Store", "t-opening-stock": "Opening stock quantity", "t-enable-shipping": "Enable shipping for this product", "t-price-tooltip": "Amount the customer needs to pay", "t-comparative-price-tooltip": "The amount before discount, shows the discount value, incentives for customers", "t-cost-price-tooltip": "Purchase price before any markup", "t-sku-tooltip": "SKU, product code for each product should be unique, including both letters and numbers", "t-personal-details": "Personal Details", "t-change-password": "Change Password", "t-confirm-change": "Confirm change", "t-old-password": "Old password", "t-new-password": "New password", "t-barcode-tooltip": "Barcode - Barcodes are usually created by the Manufacturer", "t-measurement-unit": "Measurement unit", "t-unit-measurement": "Multiple units of measurement", "t-basic-unit": "Basic unit", "t-basic-unit-tooltip": "Smallest unit of product", "t-add-unit": "Add other unit", "t-quantity": "Quantity", "t-quantity-tooltip": "Conversion quantity compared to base unit", "t-grams": "grams", "t-sell-unit": "<PERSON>ll", "t-conversion-unit": "Conversion unit", "t-sell-unit-tooltip": "Select if you want to sell using this unit", "t-sign-up-maybi": "Sign up | Maybi E-commerce", "t-edit-role": "Edit role", "t-notice": "Notice", "t-notice-change-password": " - You will be required to sign in again after changing your password.", "t-permissions-list": "Permissions list", "t-refresh-permission": "Refresh Permission", "t-route": "Route", "t-search-name": "Search by name...", "t-search-by-role": "Search by role", "t-design-and-develop": "Design & develop by Networld Solution", "t-view": "View", "t-edit": "Edit", "t-delete": "Delete", "t-enter-last-name": "Enter your last name", "t-enter-first-name": "Enter your first name", "t-enter-email": "Enter your email", "t-enter-username": "Enter your username (optional)", "t-enter-password": "Enter your password", "t-valid-username": "3-150 characters: letters, numbers, _ or .", "t-select-image": "Select image", "t-select-banner": "Select banner", "t-choose-birthday": "Choose your birthday", "t-enter-phone": "Enter your phone number", "t-permission": "Permission", "t-enter-name": "Enter name", "t-enter-role-name": "Enter role name", "t-variant": "<PERSON><PERSON><PERSON>", "t-unit-variants": "This product has many variations. For example, different sizes, colors", "t-properties": "Properties", "t-add-properties": "Add properties", "t-value": "Value", "t-done": "Done", "t-new-property": "New property", "t-save": "Save", "t-create-property": "Create property", "t-edit-variant": "Edit variant", "t-edit-price": "Edit price", "t-edit-comparative-price": "Edit comparative price", "t-selected": "Selected", "t-create-this-variants": "Create this variant", "t-beginning-cost": "Beginning cost of each product", "t-edit-quantity": "Edit quantity", "t-select-location": "Select location", "t-edit-sku": "Edit SKU", "t-edit-barcode": "Edit barcode", "t-category-list": "Category list", "t-enter-slug": "Enter slug", "t-enter-link": "Enter link", "t-choose-parent": "--- Choose parent ---", "t-active": "Active", "t-inactive": "Inactive", "t-deleted": "Deleted", "t-archive": "Archive", "t-active-status": "Active", "t-inactive-status": "Inactive", "t-archive-status": "Archive"}