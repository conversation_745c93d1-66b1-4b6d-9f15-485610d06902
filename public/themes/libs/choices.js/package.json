{"name": "choices.js", "version": "9.1.0", "description": "A vanilla JS customisable text input/select box plugin", "main": "./public/assets/scripts/choices.js", "types": "./types/index.d.ts", "scripts": {"start": "run-p js:watch css:watch", "build": "run-p js:build css:build", "lint": "run-p lint:js lint:scss", "lint:js": "eslint src/scripts/**/*.ts", "lint:scss": "stylelint src/**/*.scss", "bundlesize": "bundlesize", "cypress:run": "cypress run --browser chrome", "cypress:open": "cypress open", "cypress:ci": "cypress run --browser chrome --record --group $GITHUB_REF --ci-build-id $GITHUB_SHA", "test": "run-s test:unit test:e2e", "test:unit": "cross-env TS_NODE_TRANSPILE_ONLY=true NODE_ENV=test mocha", "test:unit:watch": "npm run test:unit -- --watch --inspect=5556", "test:unit:coverage": "NODE_ENV=test nyc --reporter=lcov --reporter=text --reporter=text-summary mocha", "test:e2e": "run-p --race start cypress:run", "js:watch": "cross-env NODE_ENV=development node server.js", "js:build": "webpack --config webpack.config.prod.js", "css:watch": "nodemon -e scss -x \"npm run css:build\"", "css:build": "run-s css:sass css:prefix css:min", "css:sass": "sass -I scss src/styles/base.scss public/assets/styles/base.css && sass -I scss src/styles/choices.scss public/assets/styles/choices.css", "css:prefix": "postcss public/assets/styles/*.css --use autoprefixer --no-map --env prod --dir public/assets/styles", "css:min": "csso public/assets/styles/base.css --output public/assets/styles/base.min.css && csso public/assets/styles/choices.css --output public/assets/styles/choices.min.css", "deploy": "git subtree push --prefix public origin gh-pages", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/jshjohnson/Choices.git"}, "author": "<PERSON>", "license": "MIT", "files": ["public/assets/scripts", "public/assets/styles", "src", "!src/**/*.test.js", "types"], "bugs": {"url": "https://github.com/jshjo<PERSON>son/Choices/issues"}, "homepage": "https://github.com/jshjo<PERSON>son/Choices#readme", "keywords": ["customisable", "input", "select", "vanilla", "plugin", "js"], "devDependencies": {"@babel/core": "^7.16.5", "@babel/preset-env": "^7.16.5", "@babel/register": "^7.16.5", "@types/chai": "^4.3.0", "@types/mocha": "^9.0.0", "@types/sinon": "^10.0.6", "@types/sinon-chai": "^3.2.6", "@typescript-eslint/eslint-plugin": "^5.7.0", "@typescript-eslint/parser": "^5.7.0", "autoprefixer": "^10.4.0", "babel-loader": "^8.2.3", "bundlesize": "^0.18.1", "chai": "^4.3.4", "cross-env": "^7.0.3", "csso-cli": "^3.0.0", "cypress": "9.1.1", "eslint": "^8.5.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^16.1.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-compat": "4.0.0", "eslint-plugin-cypress": "^2.12.1", "eslint-plugin-import": "^2.25.3", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-sort-class-members": "^1.14.1", "eslint-webpack-plugin": "^3.1.1", "express": "^4.17.2", "husky": "^7.0.4", "jsdom": "^19.0.0", "lint-staged": "^12.1.3", "mocha": "^9.1.3", "nodemon": "^2.0.15", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "postcss": "^8.4.5", "postcss-cli": "^9.1.0", "prettier": "^2.5.1", "sass": "^1.45.0", "sinon": "^12.0.1", "sinon-chai": "^3.7.0", "stylelint": "^14.1.0", "stylelint-config-standard": "^24.0.0", "stylelint-config-standard-scss": "^3.0.0", "ts-loader": "^9.2.6", "ts-node": "^10.4.0", "typescript": "^4.5.4", "webpack": "^5.65.0", "webpack-cli": "^4.9.1", "webpack-dev-middleware": "^5.3.0", "webpack-hot-middleware": "^2.25.1"}, "dependencies": {"deepmerge": "^4.2.2", "fuse.js": "^3.4.6", "redux": "^4.1.2"}, "npmName": "choices.js", "npmFileMap": [{"files": ["public/assets/scripts/*", "public/assets/styles/*", "src/icons/*"]}], "nyc": {"include": ["src/scripts/**/**/*.js"], "exclude": ["src/scripts/**/**/*.test.js"]}, "bundlesize": [{"path": "public/assets/scripts/choices.min.js", "maxSize": "25 kB"}, {"path": "public/assets/styles/choices.min.css", "maxSize": "1.8 kB"}]}