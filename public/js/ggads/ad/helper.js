import { evaluateMetricWithMathJS } from "/static/js/ggads/adgroup/config-table.js";

export const onUpdateSelectedRowTableToURL = (selects, param, keyId) => {
    const selectedIds = [];
    if (selects.length === 0) {
        removeParamURL(param);
    } else {
        selects.forEach((select) => {
            selectedIds.push(select["DT_RowId"].replace("row_", ""));
        });
        setParamURL(param, selectedIds.join(","));
    }
};

export const onDefaultTable = (tableElRef, table, param, callBackRowChange) => {
    const checkSelectPath = "td:first-child input[type=checkbox].row-select";

    // Update page to url
    tableElRef.on("page.dt", function (event) {
        const page = table.page.info();
        setParamURL("page", page.page + 1);
    });

    table.on("draw.dt", () => {
        const adgroupSelected = getParamURL(param);
        if (!adgroupSelected) return;
        const adgroupsChecked = adgroupSelected.split(",");
        tableElRef.find("tbody tr").each((idx, item) => {
            const rowId = $(item).attr("id")?.replace("row_", "");
            if (adgroupsChecked.includes(rowId)) {
                $(item).addClass("selected");
                $(item).find(checkSelectPath).prop("checked", true);
                table.row(item).select();
            } else {
                $(item).removeClass("selected");
                $(item).find(checkSelectPath).prop("checked", false);
                table.row(item).deselect();
            }
        });
    });

    // On click row
    tableElRef.on("click", "tr", function (event) {
        if ($(event.target).is(checkSelectPath)) {
            return;
        }
        tableElRef.find("tbody tr").each((idx, item) => {
            $(item).removeClass("selected");
            $(item).find(checkSelectPath).prop("checked", false);
            table.row(item).deselect();
        });
        $(this).addClass("selected");
        $(this).find(checkSelectPath).prop("checked", true);
        table.row(this).select();
    });

    // on select row
    table.on("select", function (e, dt, type, indexes) {
        if (type === "row") {
            callBackRowChange();
        }
    });

    // on deselect row
    table.on("deselect", function (e, dt, type, indexes) {
        if (type === "row") {
            callBackRowChange();
        }
    });
};

export const onSelectAllRow = (tableElRef, table, callBack) => {
    tableElRef.find("#checkAll").on("click", function () {
        const isChecked = $(this).prop("checked");
        $(".row-select").prop("checked", isChecked);

        if (isChecked) {
            table.rows({ page: "current" }).select();
        } else {
            table.rows({ page: "current" }).deselect();
        }
        callBack();
    });
};

export const cellPercific = {
    budget: "bugetCellTable",
    optimization_score: "optimizationScoreCellTable",
};

/**
 * Get Custom Columns for adgrou[ Table
 */
export function getCustomColumn(customFields, level = "ADGROUP") {
    let columnTable = customFields.map((field) => {
        if (field.hasOwnProperty("available") && !field.available.includes(level)) return null; // Skip hidden fields

        const col = {
            name: field.key,
            title: field.title,
            orderable: false,
            className: field?.type === "string" ? "align-middle text-start" : "align-middle text-end",
            data: null,
        };

        // Cell specific handling for budget and optimization_goal
        switch (field.key) {
            case "budget":
                return getBugetColumnHtml(col, field, level);
            case "optimization_score":
                return getOptimizationScoreColumnHtml(col, field);
        }

        let str = $(`#numberCellTable`).html() || "";
        const { minimumFractionDigits, maximumFractionDigits } = getFractionDigits(field.type);

        // Custom metric cell
        if ("metric" in field) {
            col.data = (row) => {
                if (field.unit === "CURRENCY") {
                    field.unit = row["advertiser_currency"] || "VND";
                    field.type = getGoCurrencyType(field.unit);
                } else if (field.unit === "%") {
                    field.type = "float64"; // Ensure percentage fields are treated as float64
                }

                const { minimumFractionDigits, maximumFractionDigits } = getFractionDigits(field.type);
                const valueRaw = evaluateMetricWithMathJS(field.metric, row);
                const formattedValue = numberFormatToDisplayNumberPerCurrency(valueRaw ?? 0, field.unit, minimumFractionDigits, maximumFractionDigits);
                return str.replace(/{value}/, formattedValue);
            };
            return col;
        }

        // Raw value cell
        if (field.type === "string") {
            let str = $(`#rawValueCellTable`).html() || "";
            col.data = (row) => {
                return str.replace(/{value}/, row[field.key]);
            };
            return col;
        }

        // Default formatted cell
        col.data = (row) => {
            if (field.unit === "CURRENCY") {
                field["unit"] = row["advertiser_currency"] || "VND";
            }
            const formattedValue = numberFormatToDisplayNumberPerCurrency(row[field.key] ?? 0, field.unit, minimumFractionDigits, maximumFractionDigits);
            return str.replace(/{value}/, formattedValue);
        };

        return col;
    });

    columnTable = columnTable.filter((col) => col !== null); // Remove null entries

    return columnTable;
}

/**
 * Format number to display based on currency
 */
export function getGoCurrencyType(currency) {
    const intCurrencies = ["VND", "JPY", "KRW", "IDR", "HUF", "CLP", "COP", "ISK", "MGA", "RWF", "UGW", "XAF", "XOF"];
    return intCurrencies.includes(currency) ? "int64" : "float64";
}

/*
 * Get fraction digits based on type
 */
export function getFractionDigits(type = "int32") {
    switch (type) {
        case "int32":
        case "int64":
            return { minimumFractionDigits: 0, maximumFractionDigits: 0 };
        case "float64":
            return { minimumFractionDigits: 1, maximumFractionDigits: 2 };
        default:
            return { minimumFractionDigits: 0, maximumFractionDigits: 0 };
    }
}

/**
 * Get Optimization column HTML for adgroup
 */
export function getOptimizationScoreColumnHtml(col, field) {
    let str = $(`#${cellPercific[field.key]}`).html() || "";
    col.data = (row) => {
        if (field.unit === "CURRENCY") {
            field["unit"] = row["currency"] || "VND";
            field.type = getGoCurrencyType(field.unit);
        } else if (field.unit === "%") {
            field.type = "float64"; // Ensure percentage fields are treated as float64
        }
        const { minimumFractionDigits, maximumFractionDigits } = getFractionDigits(field.type);

        let value = 0,
            label = "";
        return str.replace(/{value}/, numberFormatToDisplayNumberPerCurrency(value, field.unit, minimumFractionDigits, maximumFractionDigits)).replace(/{label}/, label);
    };
    return col;
}

/**
 * Get budget column HTML for adgroup
 */
export function getBugetColumnHtml(col, field, level = "ADGROUP") {
    let str = $(`#${cellPercific[field.key]}`).html() || "";
    col.data = (row) => {
        if (field.unit === "CURRENCY") {
            field["unit"] = row["currency"] || "VND";
        }
        return str.replace(/{value}/, numberFormatToDisplayNumberPerCurrency(budget, field.unit)).replace(/{label}/, budgetMode) + "/day";
    };
    return col;
}
