import { successAlert, errorAlert, loader } from "/static/js/common/helpers.js";
import { requestApi } from "/static/js/common/httpService.js";
import { getFractionDigits } from "/static/js/ggads/campaign/helper.js";
import { defaultCustomFields } from "/static/js/ggads/campaign/default_fields.js";

const OFFSET_COLUMN = 4;
function disableDragColumn() {
    let isFixedCol = false;

    $(document).on("mousedown", "th[data-dt-column]", function () {
        const colIndex = parseInt($(this).data("dt-column"), 10);
        isFixedCol = colIndex < OFFSET_COLUMN;
    });

    $(document).on("mouseup", function () {
        isFixedCol = false;
    });

    $(document).on("mousemove", function (e) {
        if (isFixedCol) {
            e.stopImmediatePropagation();
        }
    });
}

export const defaultConfigTableTiktok = ({ displayStart }, titleExcel = "Ads") => {
    disableDragColumn();
    return {
        initComplete: function () {
            const api = this.api();
            const $table = $(api.table().node());
            // Gắn vào DOM table
            $table.data("lastOrder", api.colReorder.order().slice());
            api.columns().every(function (idx) {
                const header = $(this.header());
                if (idx >= OFFSET_COLUMN) {
                    header.prepend('<span class="drag-icon me-2 fs-15 text-muted" style="float: left;">⠿</span>');
                }

                const columnIdx = this.index();

                // Lấy chiều rộng hiện tại
                const currentWidth = header.outerWidth();

                // Cộng thêm 50px
                $(header).css("min-width", currentWidth + 25 + "px");
                $(header).css("max-width", currentWidth + 25 + "px");
                $(header).css("width", currentWidth + 25 + "px");
            });
            setTimeout(() => {
                api.columns.adjust().draw(false);
            }, 200);
        },
        dom: "<'d-none'B><'row'<'col-sm-12'tr>> <'row p-2'<'col-sm-6 col-md-3'l><'col-sm-6 col-md-5'i><'col-sm-12 col-md-4 d-flex justify-content-end'p>>",
        buttons: [
            {
                extend: "excelHtml5",
                title: titleExcel,
                exportOptions: {
                    columns: ":visible",
                },
                // className: "d-none",
                attr: {
                    id: "btnExportExcelTable",
                },
            },
        ],
        responsive: false,
        colReorder: true,
        serverSide: true,
        autoWidth: true,
        scrollCollapse: true,
        select: {
            style: "multi",
            selector: 'td:first-child input[type="checkbox"]',
            headerCheckbox: false,
        },
        lengthChange: true,
        lengthMenu: [
            [10, 50, 100],
            [10, 50, 100],
        ],
        scrollY: "62vh",
        scrollX: true,
        fixedColumns: {
            leftColumns: 3,
        },
        processing: true,
        order: [],
        pageLength: 100,
        paging: true,
        displayStart,
        language: {
            zeroRecords: strHTMLNoDataTable,
            emptyTable: strHTMLNoDataTable,
        },
    };
};

/**
 * Evaluate a metric expression using MathJS
 */
export function evaluateMetricWithMathJS(metric, row) {
    const expression = metric.replace(/{(.*?)}/g, (_, key) => {
        const value = row[key];
        return typeof value === "number" ? value : 0;
    });

    try {
        const result = math.evaluate(expression);
        if (!isFinite(result) || isNaN(result)) {
            return 0;
        }
        return result;
    } catch (e) {
        console.error("MathJS evaluation error:", e);
        return null;
    }
}

export const strHTMLNoDataTable = `
                  <div class="no-results">
                     <img src="/static/images/no-data.png" alt="No Results" style="max-width: 250px; margin-bottom: 10px;">
                     <p>No Data</p>
                  </div>`;

export function HandlerError(xhr, status, error, idTable) {
    const table = $(`#${idTable}`).DataTable();
    const columnCount = table.columns().count();
    $(`#${idTable}_processing`).hide();
    $(`#${idTable} tbody`).html(`
        <tr>
            <td colspan="${columnCount}" class="dt-empty">
                  ${strHTMLNoDataTable}
            </td>
        </tr>
    `);
    try {
        let response = JSON.parse(xhr.responseText);
        Alert.error(response?.data?.msg || "Get data error!");
    } catch (e) {
        Alert.error(e || "Get data error!");
    }
    // console.error("Unknown error:", error);
}

export const getHeaderTable = (thead) => {
    if (!thead) {
        throw new Error("The thead element is required.");
    }
    let theadCustom = ``;
    thead.forEach((col) => {
        theadCustom += `
         <th>${col}</th>
      `;
    });
    return `
    <thead class="table-light">
        <tr>
            <th scope="col">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="checkAll" value="option">
                </div>
            </th>
           ${theadCustom}
        </tr>
    </thead>`;
};

/**
 * Get sum of field table
 */
function getSumDataByName(dataTable, key) {
    let total = 0;
    dataTable.rows().every(function (rowIdx, tableLoop, rowLoop) {
        var rowData = this.data();
        if (rowData.hasOwnProperty(key)) {
            const value = rowData[key];
            if (typeof value === "number") {
                total += value;
            } else if (typeof value === "string") {
                const number = parseFloat(value.replace(/[^0-9.-]+/g, ""));
                total += isNaN(number) ? 0 : number;
            }
        }
    });
    return total;
}

/**
 * Render footer for table by metric
 */
export function renderFooterTableByMetric(tableElRef, dataTable, titleTab, fieldsRaw, level = "ADGROUP") {
    let fields = fieldsRaw.filter((field) => {
        if (!field.hasOwnProperty("available")) return field;
        if (field.available.includes(level)) return field; // Skip hidden fields
    });

    function createFooter() {
        const rowCount = dataTable.rows({ page: "current" }).count();
        const titleFootTemplate = $("#titleFootTableHtml").html();
        const totalFootTemplate = $("#totalFootTableHtml").html();
        const nullFootHtml = $("#nullFootTableHtml").html();
        const keysMetric = extractUniqueMetricKeys(fields);
        const fieldsTotal = {};

        fields.forEach((field, index) => {
            if (field.unit === "CURRENCY") {
                field["unit"] = dataTable.rows()[0]["advertiser_currency"] || "VND";
            }
            if (field?.type === "string" || field?.is_custom_cell) {
                // fieldsTotal[field.key] = 0;
                fields[index].html = nullFootHtml;
                return;
            } else if (!field.hasOwnProperty("metric")) {
                const value = getSumDataByName(dataTable, field.key);
                // fieldsTotal[field.key] = value;
                fields[index].html = generateFooterHtml(value, field, totalFootTemplate);
            }
        });

        // Calculate total for each metric key
        keysMetric.forEach((key) => {
            const value = getSumDataByName(dataTable, key);
            fieldsTotal[key] = value;
        });

        // Second loop: Evaluate metric fields using values from fieldsTotal
        fields.forEach((field, index) => {
            if (field.unit === "CURRENCY") {
                field["unit"] = dataTable.rows()[0]["advertiser_currency"] || "VND";
            }
            if (field.hasOwnProperty("metric")) {
                const value = evaluateMetricWithMathJS(field.metric, fieldsTotal);
                fields[index].html = generateFooterHtml(value, field, totalFootTemplate);
            }
        });

        // Tạo HTML footer
        const footerHtml = `
            <tr>
                ${titleFootTemplate.replace(/{rowCount}/g, rowCount).replace(/{title}/g, titleTab)}
                ${nullFootHtml}
                ${fields.map((f) => f.html).join("")}
            </tr>
        `;

        $(dataTable.table().footer()).html(footerHtml);
    }

    tableElRef.on("preDraw.dt", createFooter);

    dataTable.on(
        "column-reorder",
        debounce(function (e, settings, details) {
            /** Disabled drag into column disabled drag */
            const api = $(this).DataTable();
            const from = details.from;
            const to = details.to;

            if (from[0] < OFFSET_COLUMN || to < OFFSET_COLUMN) {
                const lastOrder = tableElRef.data("lastOrder") || [];
                api.colReorder.order(lastOrder, true); // reset lại
                return;
            }

            // api.colReorder._lastOrder = api.colReorder.order();
            /** End Disabled drag into column disabled drag */

            const newOrderKeysRaw = dataTable
                .columns(":visible")
                .indexes()
                .toArray()
                .map((idx) => dataTable.settings()[0].aoColumns[idx].name);

            const newOrderKeys = newOrderKeysRaw.slice(3);

            // Tạo map từ key -> field
            const fieldsMap = {};
            fields.forEach((field) => {
                if (field.key) {
                    fieldsMap[field.key] = field;
                }
            });

            // Tạo mảng mới theo thứ tự newOrderKeys
            const reorderedFields = [];
            newOrderKeys.forEach((key) => {
                if (fieldsMap[key]) {
                    reorderedFields.push(fieldsMap[key]);
                }
            });

            fields.length = 0;
            reorderedFields.forEach((field) => fields.push(field));

            createFooter();
        }, 60)
    );

    return createFooter;
}

/**
 * Extract unique metric keys from columns
 */
function extractUniqueMetricKeys(columns) {
    const keySet = new Set();
    const regex = /\{(.*?)\}/g;

    columns.forEach((col) => {
        if (col.metric) {
            let match;
            while ((match = regex.exec(col.metric)) !== null) {
                keySet.add(match[1]);
            }
        }
    });

    return Array.from(keySet);
}

// Helper tách phần render HTML field
function generateFooterHtml(value, field, template) {
    const { minimumFractionDigits, maximumFractionDigits } = getFractionDigits(field.type);
    const formattedValue = numberFormatToDisplayNumberPerCurrency(value ?? 0, field.unit, minimumFractionDigits, maximumFractionDigits);

    const titleTotal = field.unit !== "%" ? "total" : "";
    return template.replace(/{value}/g, formattedValue).replace(/{titleTotal}/g, titleTotal);
}

// Helper tách phần render HTML field
export function generateCellTableHtml(value, field, template) {
    const { minimumFractionDigits, maximumFractionDigits } = getFractionDigits(field.type);
    const formattedValue = numberFormatToDisplayNumberPerCurrency(value ?? 0, field.unit, minimumFractionDigits, maximumFractionDigits);

    const titleTotal = field.unit !== "%" ? "total" : "";
    return template.replace(/{value}/g, formattedValue).replace(/{titleTotal}/g, titleTotal);
}

/**
 * Get Custom Field Table
 */
export async function getCustomFieldTable() {
    loader();
    let presetColumn = localStorage.getItem("preset_column");
    let payload = {};
    if (presetColumn) {
        payload = {
            preset_column_id: presetColumn,
        };
    }
    return requestApi("POST", `/dsp/tiktok/api/custom-column-table/details-preset-column`, payload, { timeout: 3000 })
        .then((res) => {
            let result = [];
            if (res?.data?.data.length > 0) {
                if (res?.data?.preset_name) {
                    $("#listTableRighttHeader #presetNameView").text(res?.data?.preset_name);
                    $("#presetColumnDropdown #presetNameView").text(res?.data?.preset_name);
                } else {
                    $("#listTableRighttHeader #presetNameView").text("Default");
                    $("#presetColumnDropdown #presetNameView").text("Default");
                    localStorage.removeItem("preset_column");
                }
                result = res.data.data;
            } else {
                result = defaultCustomFields;
                localStorage.removeItem("preset_column");
            }

            window.adsTable = {
                customFields: result,
            };
            return result;
        })
        .catch((error) => {
            console.error("Error fetching custom fields:", error);
            window.adsTable = {
                customFields: defaultCustomFields,
            };
            localStorage.removeItem("preset_column");
            return defaultCustomFields;
        })
        .finally(() => {
            loader(false);
        });
}

// ----------------------- Cell Custum HTML -----------------------
export function getBidStrategyType(ad) {
    // Default
    return {
        value: 0,
        label: "-",
    };
}

$(document).on("mousemove", "th.dtfc-fixed-left", function (e) {
    e.stopImmediatePropagation();
});
