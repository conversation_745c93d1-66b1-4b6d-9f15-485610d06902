import { getCookie } from "/static/js/common/helpers.js";
import { onUpdateSelectedRowTableToURL, onDefaultTable, onSelectAllRow, getCustomColumn } from "/static/js/ggads/adgroup/helper.js";
import { defaultConfigTableTiktok, HandlerError, getHeaderTable, renderFooterTableByMetric, getCustomFieldTable } from "/static/js/ggads/adgroup/config-table.js";
import { CP_STATUS, CP_STATUS_SERVING, CP_STATUS_SERVING_TITLE, DATETIME_FORMAT_TYPES_UTC } from "/static/js/ggads/adgroup/constants.js";

let isFristLoad = true;
const ID_TABLE = "data-table-adgroup";
const tableSelector = "table#data-table-adgroup";
const tableElRef = $(tableSelector);
const headerTable = ["Off/On"];
let customFields = [];
const eventManager = {
    renderFooter: null,
};
let datatable;
const filterData = { filter: { search: "", page: 1, start: 0, time: null, campaign_ids: [], adgroup_id: "", userId: "", advertiser_id: "", client_id: "" } };

/**
 * Init Table p
 */

const initTable = function () {
    tableElRef.html(getHeaderTable(headerTable));
    datatable = tableElRef.DataTable({
        ...defaultConfigTableTiktok({ displayStart: filterData.filter.start }, "ad_groups"),
        columns: [
            {
                data: null,
                orderable: false,
                searchable: false,
                width: "20px",
                className: "align-middle",
                render: function (data, type, row) {
                    return `<div class="form-check" >
                                    <input class="form-check-input row-select" type="checkbox" name="chk_child" value="${row.adgroup_id}">
                                </div>`;
                },
            },
            {
                title: "Off/On",
                orderable: false,
                width: "30px",
                className: "align-middle",
                data: function (row) {
                    return `
                        <div class="form-check form-switch form-check-inline" dir="ltr">
                            <input
                                type="checkbox"
                                class="form-check-input form-switch-md"
                                id="switch-check-${row.adgroup_id}"
                                ${row.status === CP_STATUS.ENABLED ? "checked" : ""} />
                        </div>
                    `;
                },
            },
            {
                title: "Ad group",
                name: "adgroup_name",
                render: function (data, type, row) {
                    return `
                        <div class="name-blk">
                            <a href="#" class="text-nowrap-3" title="${row.adgroup_name}">${row.adgroup_name}</a>
                            <div class="action-blk">
                                <button class="btn p-0 me-1" hidden><i class="ri-bar-chart-box-line"></i> Charts</button>                         
                                <button
                                    class="btn p-0 me-1 btn-edit-campaign btn-small"
                                    data-bs-toggle="offcanvas"
                                    data-bs-target="#OffCanvasCampAdgroupAd"
                                    aria-controls="OffCanvasCampAdgroupAd"><i class="ri-pencil-fill"></i> Edit</button>                         
                                 <button class="btn p-0 me-1" hidden><i class="ri-file-copy-2-fill"></i> Duplicate</button>                         
                                 <button class="btn p-0 me-1" hidden><i class="ri-pushpin-fill"></i> Pin</button>                         
                            </div>
                        </div>
                    `;
                },
            },
            {
                title: "Campaign",
                name: "campaign_name",
                render: function (data, type, row) {
                    return `
                        <div class "campaign_name_blk">
                            <a href="#" class="text-nowrap-3" title="${row.campaign_name}">${row.campaign_name}</a>
                        </div>
                    `
                }
            },
            {
                title: "Status",
                name: "secondary_status",
                className: "align-middle",
                data: function (row) {
                    return `<i class="ri-question-circle-fill text-warning align-middle">${CP_STATUS_SERVING_TITLE[row.servingStatus]}</i>`;
                },
            },
            ...getCustomColumn(customFields, "ADGROUP"),
        ],
        ajax: onAjaxTable,
    });

    // if (isFristLoad) {
    initAddEventForTable();
    // }
    tableElRef.off("preDraw.dt", eventManager.renderFooter);
    eventManager.renderFooter = renderFooterTableByMetric(tableElRef, datatable, "adgroups", customFields, "ADGROUP");
};

/**
 * Update filter table
 */
function updateFilterTable() {
    let currentPage = getParamURL("page");
    filterData.filter.page = currentPage || 1;
    currentPage = currentPage ? parseInt(currentPage, 10) : 1;
    filterData.filter.start = (currentPage - 1) * 10;

    const datePicker = $("#filter-search-datetime").data("daterangepicker");
    const start = datePicker.startDate.format(DATETIME_FORMAT_TYPES_UTC);
    const end = datePicker.endDate.format(DATETIME_FORMAT_TYPES_UTC);

    filterData.filter.time = [start, end];

    filterData.filter.client_id = $(`#clientsChoices`).val();
    filterData.filter.advertiser_id = $(`#advertiserChoices`).val();
    // filterData.filter.status = $(`#statusSelection #statusFilterSelected`).val();
    filterData.filter.search = $(`#valueSearchTable`).val();
    filterData.filter.adgroup_id = $(`#valueSearchTable`).val();

    console.log(" 🚀 ~ updateFilterTable ~ filterData:", filterData);
}

/**
 * Ajax config
 */
function onAjaxTable(data, callback) {
    updateFilterTable();
    console.log("url: ", $(`#${ID_TABLE}`).data("url"))
    $.ajax({
        url: $(`#${ID_TABLE}`).data("url"),
        type: "POST",
        contentType: "application/x-www-form-urlencoded",
        headers: {
            "X-CSRF-Token": getCookie("csrf_"), // Hàm lấy CSRF token
        },
        data: {
            ...data,
            ...filterData.filter,
        },
        success: function (response) {
            callback({
                data: response.data,
                recordsTotal: response.recordsTotal, // Tổng số bản ghi
                recordsFiltered: response.recordsFiltered, // Số bản ghi sau khi filter
            });
        },
        error: function (xhr, status, error) {
            HandlerError(xhr, status, error, ID_TABLE);
        },
    });
}

/**
 * On Select Row Change
 */
const onSelectRowChange = () => {
    const table = $(tableSelector).DataTable();
    const selects = table.rows({ selected: true }).data().toArray();

    onUpdateSelectedRowTableToURL(selects, "selected_adgroup_ids", "adgroup_id");
    // if (!selects || selects.length === 0) {
    //     nameAdgroupSelected.text("Adgroups");
    //     nameAdSeltected.text("Ads");
    //     return $("#camp_selected").addClass("d-none");
    // }

    // $("#camp_selected")
    //     .contents()
    //     .filter(function () {
    //         return this.nodeType === Node.TEXT_NODE && this.nodeValue.trim() !== "";
    //     })
    //     .first()
    //     .replaceWith(selects.length + " selected");

    // $("#camp_selected").removeClass("d-none");

    // nameAdgroupSelected.text(`Adgroups for ${selects.length} Campaign`);
    // nameAdSeltected.text(`Ads for  ${selects.length} Campaign`);
};

/**
 * Init Add Event For Table
 */
function initAddEventForTable() {
    onDefaultTable(tableElRef, datatable, "selected_adgroup_ids", onSelectRowChange);

    // On Select All
    onSelectAllRow(tableElRef, datatable, onSelectRowChange);

    /**
     * Edit Adgroup
     */
    tableElRef.on("click", "tr td button.btn-edit-adgroup", function () {
        const tr = $(this).closest("tr");
        const id = tr.attr("id");
        if (id) {
            const router = new Navigo("/");
            router.navigate(`${window.location.origin}?edit_adgroup_id=${id.replace("row_", "")}&${getCurrentQuery()}`);
        }
    });
}

/**
 * On Close Adgroup select
 */
const onCloseSelected = () => {
    $("#adgroup_selected #adgroup-selected-close").on("click", () => {
        datatable.rows({ page: "current" }).deselect();
        datatable.$("input.row-select").prop("checked", false); // Uncheck checkboxes
    });
};

//On Update Filter Table
function onUpdateFilterTable() {
    document.addEventListener("onUpdateFilterTable", (event) => {
        if (!datatable) return;
        const currentConfig = datatable.settings().toArray()[0].oInit;

        if (currentConfig.bServerSide) {
            datatable.ajax.reload();
        } else {
            // console.log("init table");
            $(`#${ID_TABLE}`).DataTable().destroy();
            $(`#${ID_TABLE}`).empty();
            initTable();
        }
    });
}

/**
 * Change config column table
 */
async function inChangeConfigColumnTable() {
    document.addEventListener("onChangeConfigColumnTable", async function (event) {
        if ($(".tab-pane.active#adgroup").length > 0) {
            datatable.destroy();
            $(`#${ID_TABLE}`).empty();
            customFields = await getCustomFieldTable();
            initTable();
        }
    });
}

async function initAdgroup() {
    customFields = await getCustomFieldTable();
    initTable();
    onCloseSelected();
}

function init() {
    isFristLoad = false;
    initAdgroup();
    inChangeConfigColumnTable();
    onUpdateFilterTable();
}

$().ready(function () {
    init();
});
