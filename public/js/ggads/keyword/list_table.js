import { getCookie } from "/static/js/common/helpers.js";
import { onUpdateSelectedRowTableToURL, onDefaultTable, onSelectAllRow, getCustomColumn } from "/static/js/ggads/campaign/helper.js";
import { defaultConfigTableTik<PERSON>, HandlerError, getHeaderTable, renderFooterTableByMetric, getCustomFieldTable } from "/static/js/ggads/campaign/config-table.js";
import { CP_STATUS, CP_STATUS_SERVING, CP_STATUS_SERVING_TITLE, DATETIME_FORMAT_TYPES_UTC } from "/static/js/ggads/campaign/constants.js";

let isFristLoad = true;
const ID_TABLE = "data-table-keyword";
const tableSelector = "table#data-table-keyword";
const tableElRef = $(tableSelector);
const headerTable = ["Off/On"];
let customFields = [];
const eventManager = {
    renderFooter: null,
};
let datatable;
const filterData = { filter: { search: "", page: 1, start: 0, time: null, keyword_id: "", adgroup_id: "", campaign_id: "", advertiser_id: "", client_id: "" } };

/**
 * Init Table
 */
const initTable = function () {
    tableElRef.html(getHeaderTable(headerTable));
    datatable = tableElRef.DataTable({
        ...defaultConfigTableTiktok({ displayStart: filterData.filter.start }, "keywords"),
        columns: [
            {
                data: null,
                orderable: false,
                searchable: false,
                width: "20px",
                className: "align-middle",
                render: function (data, type, row) {
                    return `<div class="form-check" >
                                    <input class="form-check-input row-select" type="checkbox" name="chk_child" value="${row.keyword_id}">
                                </div>`;
                },
            },
            {
                title: "Off/On",
                orderable: false,
                width: "30px",
                className: "align-middle",
                data: function (row) {
                    return `
                        <div class="form-check form-switch form-check-inline" dir="ltr">
                            <input
                                type="checkbox"
                                class="form-check-input form-switch-md"
                                id="switch-check-${row.keyword_id}"
                                ${row.status === CP_STATUS.ENABLED ? "checked" : ""} />
                        </div>
                    `;
                },
            },
            {
                title: "Keyword",
                name: "keyword_text",
                render: function (data, type, row) {
                    return `
                        <div class="name-blk">
                            <a href="#" class="text-nowrap-3" title="${row.keyword_text}">${row.keyword_text}</a>
                            <div class="action-blk">
                                <button class="btn p-0 me-1" hidden><i class="ri-bar-chart-box-line"></i> Charts</button>                         
                                <button
                                    class="btn p-0 me-1 btn-edit-keyword btn-small"
                                    data-bs-toggle="offcanvas"
                                    data-bs-target="#OffCanvasKeyword"
                                    aria-controls="OffCanvasKeyword"><i class="ri-pencil-fill"></i> Edit</button>                         
                                 <button class="btn p-0 me-1" hidden><i class="ri-file-copy-2-fill"></i> Duplicate</button>                         
                                 <button class="btn p-0 me-1" hidden><i class="ri-pushpin-fill"></i> Pin</button>                         
                            </div>
                        </div>
                    `;
                },
            },
            {
                title: "AdGroup",
                name: "adgroup_name",
                render: function (data, type, row) {
                    return `<span title="${row.adgroup_name}">${row.adgroup_name}</span>`;
                },
            },
            {
                title: "Campaign",
                name: "campaign_name",
                render: function (data, type, row) {
                    return `<span title="${row.campaign_name}">${row.campaign_name}</span>`;
                },
            },
            {
                title: "Status",
                name: "status",
                className: "align-middle",
                data: function (row) {
                    const statusClass = row.status === 'ENABLED' ? 'text-success' : 'text-warning';
                    return `<span class="${statusClass}">${row.status}</span>`;
                },
            },
            {
                title: "Match Type",
                name: "match_type",
                render: function (data, type, row) {
                    return `<span title="${row.match_type}">${row.match_type}</span>`;
                },
            },
            {
                title: "Impressions",
                name: "impressions",
                className: "text-end",
                render: function (data, type, row) {
                    return `<span>${row.impressions || 0}</span>`;
                },
            },
            {
                title: "Clicks",
                name: "clicks",
                className: "text-end",
                render: function (data, type, row) {
                    return `<span>${row.clicks || 0}</span>`;
                },
            },
            {
                title: "CTR",
                name: "ctr",
                className: "text-end",
                render: function (data, type, row) {
                    const ctr = row.impressions > 0 ? ((row.clicks / row.impressions) * 100).toFixed(2) : 0;
                    return `<span>${ctr}%</span>`;
                },
            },
            {
                title: "Avg CPC",
                name: "average_cpc",
                className: "text-end",
                render: function (data, type, row) {
                    return `<span>₫${(row.average_cpc || 0).toLocaleString()}</span>`;
                },
            },
            ...getCustomColumn(customFields, "KEYWORD"),
        ],
        ajax: onAjaxTable,
    });

    initAddEventForTable();
    tableElRef.off("preDraw.dt", eventManager.renderFooter);
    eventManager.renderFooter = renderFooterTableByMetric(tableElRef, datatable, "keywords", customFields, "KEYWORD");
};

/**
 * Update filter table
 */
function updateFilterTable() {
    let currentPage = getParamURL("page");
    filterData.filter.page = currentPage || 1;
    currentPage = currentPage ? parseInt(currentPage, 10) : 1;
    filterData.filter.start = (currentPage - 1) * 10;

    const datePicker = $("#filter-search-datetime").data("daterangepicker");
    if (datePicker) {
        const start = datePicker.startDate.format(DATETIME_FORMAT_TYPES_UTC);
        const end = datePicker.endDate.format(DATETIME_FORMAT_TYPES_UTC);
        filterData.filter.time = [start, end];
    }

    filterData.filter.client_id = $(`#clientsChoices`).val();
    filterData.filter.advertiser_id = $(`#advertiserChoices`).val();
    filterData.filter.search = $(`#valueSearchTable`).val();
    filterData.filter.keyword_id = $(`#valueSearchTable`).val();

    console.log(" 🚀 ~ updateFilterTable ~ filterData:", filterData);
}

/**
 * Ajax config
 */
function onAjaxTable(data, callback) {
    updateFilterTable();
    $.ajax({
        url: $(`#${ID_TABLE}`).data("url"),
        type: "POST",
        contentType: "application/json",
        headers: {
            "X-CSRF-Token": getCookie("csrf_"),
        },
        data: JSON.stringify({
            draw: data.draw,
            start: data.start,
            length: data.length,
            search: data.search.value || null,
            order: data.order.map(function(order) {
                return {
                    column: order.column,
                    dir: order.dir
                };
            }),
            ...filterData.filter,
        }),
        success: function (response) {
            callback({
                data: response.data,
                recordsTotal: response.recordsTotal,
                recordsFiltered: response.recordsFiltered,
            });
        },
        error: function (xhr, status, error) {
            HandlerError(xhr, status, error, ID_TABLE);
        },
    });
}

/**
 * On Select Row Change
 */
const onSelectRowChange = () => {
    const table = $(tableSelector).DataTable();
    const selects = table.rows({ selected: true }).data().toArray();
    onUpdateSelectedRowTableToURL(selects, "selected_keyword_ids", "keyword_id");
};

/**
 * Init Add Event For Table
 */
function initAddEventForTable() {
    onDefaultTable(tableElRef, datatable, "selected_keyword_ids", onSelectRowChange);
    onSelectAllRow(tableElRef, datatable, onSelectRowChange);

    /**
     * Edit Keyword
     */
    tableElRef.on("click", "tr td button.btn-edit-keyword", function () {
        const tr = $(this).closest("tr");
        const id = tr.attr("id");
        if (id) {
            const router = new Navigo("/");
            router.navigate(`${window.location.origin}?edit_keyword_id=${id.replace("row_", "")}&${getCurrentQuery()}`);
        }
    });
}

//On Update Filter Table
function onUpdateFilterTable() {
    document.addEventListener("onUpdateFilterTable", (event) => {
        if (!datatable) return;
        const currentConfig = datatable.settings().toArray()[0].oInit;

        if (currentConfig.bServerSide) {
            datatable.ajax.reload();
        } else {
            $(`#${ID_TABLE}`).DataTable().destroy();
            $(`#${ID_TABLE}`).empty();
            initTable();
        }
    });
}

/**
 * Change config column table
 */
async function inChangeConfigColumnTable() {
    document.addEventListener("onChangeConfigColumnTable", async function (event) {
        if ($(".tab-pane.active#keyword").length > 0) {
            datatable.destroy();
            $(`#${ID_TABLE}`).empty();
            customFields = await getCustomFieldTable();
            initTable();
        }
    });
}

async function initKeyword() {
    customFields = await getCustomFieldTable();
    initTable();
}

function init() {
    isFristLoad = false;
    initKeyword();
    inChangeConfigColumnTable();
    onUpdateFilterTable();
}

$().ready(function () {
    init();
});

// Helper function to get URL parameters
function getParamURL(param) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(param);
}

function getCurrentQuery() {
    return window.location.search.substring(1);
}
