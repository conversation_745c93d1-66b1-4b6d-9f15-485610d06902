const replaceIdForHref = function (id, href) {
    let string = href.replace("/0/", `/${id}/`);

    return string.replace(/0$/, `${id}`);
};

const getCookie = function (name) {
    let cookieValue = document.cookie.replace(/(?:(?:^|.*;\s*)username\s*\=\s*([^;]*).*$)|^.*$/, "$1");

    if (document.cookie && document.cookie !== "") {
        var cookies = document.cookie.split(";");
        for (var i = 0; i < cookies.length; i++) {
            var cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === name + "=") {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
};

function errorAlert(msg) {
    Swal.fire({
        toast: true,
        position: "top-end",
        icon: "error",
        title: msg,
        showConfirmButton: !1,
        timer: 3500,
        showCloseButton: !0,
        timerProgressBar: true,
        didOpen: (toast) => {
            toast.onmouseenter = Swal.stopTimer;
            toast.onmouseleave = Swal.resumeTimer;
        },
    });
}
function successAlert(msg) {
    Swal.fire({
        toast: true,
        position: "top-end",
        icon: "success",
        title: msg,
        showConfirmButton: !1,
        timer: 3500,
        showCloseButton: !0,
        timerProgressBar: true,
        didOpen: (toast) => {
            toast.onmouseenter = Swal.stopTimer;
            toast.onmouseleave = Swal.resumeTimer;
        },
    });
}

function forbiddenAlert() {
    Swal.fire({
        html:
            '<div class="mt-3"><lord-icon src="' +
            domain +
            '/static/themes/json/tdrtiskw.json" trigger="loop" colors="primary:#f06548,secondary:#f7b84b" style="width:120px;height:120px"></lord-icon><div class="mt-4 pt-2 fs-15"><h4>Oops...!</h4><p class="text-muted mx-4 mb-0">You do not have permission to perform this action</p></div></div>',
        showCancelButton: !0,
        showConfirmButton: !1,
        cancelButtonClass: "btn btn-primary w-xs mb-1",
        cancelButtonText: "Back",
        buttonsStyling: !1,
        showCloseButton: !0,
    });
}

function handleAjaxError(xhr) {
    if (xhr.status === 403) {
        forbiddenAlert();
        return;
    }
    const errResponse = JSON.parse(xhr.responseText);
    const errMessages = Array.isArray(errResponse.details.msg) ? errResponse.details.msg.join(" <br>") : errResponse.details.msg;
    errorAlert(errMessages);
}

const numberFormat = function (field, delimiterSymbol = ".", decimalSymbol = ",") {
    if (typeof field !== "string") {
        throw new TypeError("Field selector must be a string.");
    }

    const cleaveNumber = document.querySelectorAll(field);
    cleaveNumber.forEach(function (field) {
        new Cleave(field, { numeral: true, numeralDecimalMark: decimalSymbol, delimiter: delimiterSymbol });
    });
};

const numberFormatToDisplay = function (number, decimals = 0, thousandSeparator = ",", decimalSeparator = ".") {
    let integerPart = String(number);
    let decimalPart = "";
    if (decimals > 0) {
        const parts = number.toFixed(decimals).split(".");
        integerPart = parts[0];
        decimalPart = parts[1];
    }

    integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, thousandSeparator);
    if (decimals > 0) {
        return integerPart + decimalSeparator + decimalPart;
    }

    return integerPart;
};

const numberFormatToDisplayNumberPerCurrency = function (number, unit = null) {
    if (number === 0 || number === "0" || number === "") {
        return "-";
    }

    // Bước 1: Làm sạch chuỗi (nếu là string)
    if (typeof number === "string") {
        // Loại bỏ mọi ký tự không phải số, dấu trừ, hoặc dấu chấm
        number = number.replace(/[^0-9.-]+/g, "");
    }

    const num = parseFloat(number);
    if (isNaN(num)) return "-";

    // Làm tròn đến 2 chữ số thập phân
    let rounded = Math.round(num * 100) / 100;
    const isInteger = rounded % 1 === 0;

    let formatted;

    if (unit === "%") {
        formatted = isInteger
            ? rounded.toLocaleString("en-US", { maximumFractionDigits: 0 })
            : rounded.toLocaleString("en-US", { minimumFractionDigits: 1, maximumFractionDigits: 2 });

        return formatted + "%";
    }

    if (unit === "đ") {
        formatted = isInteger
            ? rounded.toLocaleString("en-US", { maximumFractionDigits: 0 })
            : rounded.toLocaleString("en-US", { minimumFractionDigits: 1, maximumFractionDigits: 2 });

        return "₫" + formatted;
    }

    formatted = isInteger ? rounded.toLocaleString("en-US", { maximumFractionDigits: 0 }) : rounded.toLocaleString("en-US", { minimumFractionDigits: 1, maximumFractionDigits: 2 });

    return formatted;
};

// const numberFormatToDisplayNumberPerCurrency = function (number, unit = null) {
//     if (number === 0 || number === "0" || number === "") {
//         return "-";
//     }

//     let formatter = new Intl.NumberFormat("en-US", {
//         minimumFractionDigits: 1,
//         maximumFractionDigits: 2,
//     });

//     let formatted = formatter.format(number);
//     if (unit === "%") return formatted + "%";

//     if (unit === "đ") return "₫" + formatted;
//     return formatted;
// };

const displayNumberOrHyphen = function (number) {
    if (number === 0 || number === "0" || number === "") {
        return "-";
    }

    return numberFormatToDisplay(number);
};

const formDataToJson = function (formData) {
    const formObject = {};
    formData.forEach((value, key) => {
        const keys = key.match(/[^\[\]]+/g);
        keys.reduce((accumulator, currentValue, index) => {
            if (index === keys.length - 1) {
                if (accumulator[currentValue] !== undefined && Array.isArray(accumulator[currentValue])) {
                    accumulator[currentValue].push(value);
                } else if (accumulator[currentValue] !== undefined) {
                    accumulator[currentValue] = [accumulator[currentValue], value];
                } else if (key.includes("[]")) {
                    accumulator[currentValue] = [value];
                } else {
                    accumulator[currentValue] = value;
                }
            } else {
                if (!accumulator[currentValue]) {
                    accumulator[currentValue] = {};
                }

                return accumulator[currentValue];
            }

            return accumulator;
        }, formObject);
    });

    return formObject;
};

const convertStringToIntArray = (input) => {
    return input.map(function (value) {
        return parseInt(value);
    });
};

const convertToIdValueArray = (input) => {
    return input.map(function (value) {
        return { id: value };
    });
};

function toName(str) {
    str = str.replace(/\s+/g, " ");
    str = str.trim();
    return str;
}

// replace title or slug
function toSlug(str) {
    str = str.toLowerCase();
    str = str.replace(/--\|-/g, "");
    str = str.replace(/^\-+|\-+$/g, "");
    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
    str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i");
    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
    str = str.replace(/đ/g, "d");
    str = str.replace(/\`|\~|\!|\@|\#|\||\$|\%|\^|\&|\*|\(|\)|\+|\=|\,|\.|\/|\?|\>|\<|\'|\"|\:|\;|_/gi, "-");
    str = str.replace(/[^a-zA-Z0-9 ]/g, "");
    str = str.replace(/-+/g, "-");
    str = str.replace(/^\-+|\-+$/g, "");
    str = str.replace(/\s+/g, "-");
    str = "@" + str + "@";
    str = str.replace(/\@\-|\-\@|\@/gi, "");
    return str;
}

function toSlugSimple(str) {
    str = str.toLowerCase();
    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
    str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i");
    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
    str = str.replace(/đ/g, "d");
    str = str.replace(/\`|\~|\!|\@|\#|\||\$|\%|\^|\&|\*|\(|\)|\+|\=|\,|\.|\/|\?|\>|\<|\'|\"|\:|\;|_/gi, "-");
    str = str.replace(/[^a-zA-Z0-9\-]/g, "");
    str = str.replace(/-+/g, "-");
    str = str.replace(/\s+/g, "-");
    str = "@" + str + "@";
    str = str.replace(/\@\-|\-\@|\@/gi, "");

    return str;
}

function toSlugUppercase(str) {
    str = toSlug(str);
    str = str.toUpperCase();

    return str;
}

function loader(isBool = true) {
    var preloader = document.getElementById("preloader");

    if (isBool) {
        preloader.style.opacity = "0.4";
        preloader.style.visibility = "visible";
    } else {
        preloader.style.opacity = "0";
        preloader.style.visibility = "hidden";
    }
}

function showToolTip() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        var tooltip = new bootstrap.Tooltip(tooltipTriggerEl);

        tooltipTriggerEl.addEventListener("focusout", function () {
            tooltip.hide();
        });
        return tooltip;
    });
}

function ischeckboxcheck() {
    Array.from(document.getElementsByName("chk_child")).forEach(function (a) {
        a.addEventListener("change", function (e) {
            1 == a.checked ? e.target.closest("tr").classList.add("table-active") : e.target.closest("tr").classList.remove("table-active");
            var t = document.querySelectorAll('[name="chk_child"]:checked').length;
            e.target.closest("tr").classList.contains("table-active"), (document.getElementById("remove-actions").style.display = 0 < t ? "block" : "none");
        });
    });
    var checkboxesCount = document.querySelectorAll('.form-check-all input[type="checkbox"]').length;
    var checkedCount = document.querySelectorAll('.form-check-all input[type="checkbox"]:checked').length;
    checkboxesCount > 0 && checkboxesCount == checkedCount ? (checkAll.checked = true) : (checkAll.checked = false);
    document.getElementById("remove-actions").style.display = checkedCount > 0 ? "block" : "none";
}

function formatISODateTime(time) {
    const datePart = time.split("T")[0];
    const [year, month, day] = datePart.split("-");

    return `${day}-${month}-${year}`;
}

function getDateFromDateTime(dateTime) {
    if (dateTime === "" || dateTime.trim() === "") {
        return "";
    }

    const dateObj = new Date(dateTime);
    const day = String(dateObj.getDate()).padStart(2, "0");
    const month = String(dateObj.getMonth() + 1).padStart(2, "0");
    const year = dateObj.getFullYear();

    return `${day}-${month}-${year}`;
}

const checkObjectsEqual = function (object1, object2) {
    return deepEqual(object1, object2);
};

function deepEqual(a, b) {
    if (a === b) {
        return true;
    }

    if (typeof a !== "object" || typeof b !== "object" || a == null || b == null) {
        return false;
    }

    const keysA = Object.keys(a),
        keysB = Object.keys(b);
    if (keysA.length !== keysB.length) {
        return false;
    }

    for (let key of keysA) {
        if (!keysB.includes(key) || !deepEqual(a[key], b[key])) {
            return false;
        }
    }

    return true;
}

const convertToSnakeCase = function (str) {
    return str.replace(/([a-z])([A-Z])/g, "$1_$2").toLowerCase();
};

const formatNumberString = function (str) {
    return parseInt(str.replace(/[^\d]/g, ""));
};

const validateAmountField = function (rawValue) {
    const cleaned = rawValue.replace(/[^\d]/g, "");
    if (cleaned === "" || isNaN(Number(cleaned))) {
        return false;
    }

    return true;
};

function applyCurrencyFormatting(valueInput, currency) {
    const value = valueInput.replace(/[^\d]/g, "");
    if (!value) {
        return "";
    }

    return new Intl.NumberFormat(currency === "VND" ? "vi-VN" : "en-US", {
        style: "currency",
        currency: currency,
        minimumFractionDigits: 0,
    }).format(value);
}

export {
    replaceIdForHref,
    getCookie,
    handleAjaxError,
    numberFormat,
    numberFormatToDisplay,
    displayNumberOrHyphen,
    formDataToJson,
    convertStringToIntArray,
    convertToIdValueArray,
    toSlugUppercase,
    toName,
    toSlug,
    loader,
    showToolTip,
    ischeckboxcheck,
    formatISODateTime,
    getDateFromDateTime,
    numberFormatToDisplayNumberPerCurrency,
    checkObjectsEqual,
    convertToSnakeCase,
    validateAmountField,
    applyCurrencyFormatting,
    formatNumberString,
    successAlert,
    errorAlert,
};
