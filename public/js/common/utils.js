function formatNumber(value) {
    return new Intl.NumberFormat("en-en", {
        maximumSignificantDigits: 3,
    }).format(value);
}

function isNumber(variable) {
    return typeof variable === "number" || variable instanceof Number;
}

function debounce(func, delay) {
    let timeoutId;
    return function (...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
            func.apply(this, args);
        }, delay);
    };
}

function formDataToObject(formData) {
    const obj = {};
    for (let [key, value] of formData.entries()) {
        obj[key] = value;
    }
    return obj;
}

function updateUrlParameter(param, value) {
    // Create a URL object for the current page
    const currentUrl = new URL(window.location.href);

    // Create a URLSearchParams object from the current URL's query string
    const params = new URLSearchParams(currentUrl.search);

    // Update or add the parameter
    params.set(param, value);

    // Set the updated parameters back to the URL
    currentUrl.search = params.toString();

    // Update the browser's address bar without reloading the page
    window.history.pushState({}, "", currentUrl);
}

function isNumeric(str) {
    return /^[0-9]+$/.test(str);
}

const getCampaignEdit = () => {
    const params = new URL(window.location.href).searchParams;
    const id = params.get("edit_campaign_id");
    return id;
};

const setParamURL = (key, value) => {
    const url = new URL(window.location.href);
    const params = url.searchParams;
    params.set(key, value);
    window.history.replaceState(null, "", url);
};

const getCurrentQuery = () => {
    const urlSearchParams = new URLSearchParams(window.location.search);
    return urlSearchParams.toString();
};

const getParamURL = (key) => {
    const params = new URL(window.location.href).searchParams;
    const value = params.get(key);
    return value;
};

function removeCampaignEditURL() {
    const url = new URL(window.location.href);
    url.searchParams.delete("edit_campaign_id");
    window.history.replaceState(null, "", url.toString());
}

function removeParamURL(param) {
    const url = new URL(window.location.href);
    url.searchParams.delete(param);
    window.history.replaceState(null, "", url.toString());
}

function toastifyShow(content, status, position) {
    Toastify({
        newWindow: true,
        text: content,
        gravity: top,
        position: position ? position : "right",
        className: "bg-" + status,
        stopOnFocus: true,
        offset: {
            x: 50, // horizontal axis - can be a number or a string indicating unity. eg: '2em'
            y: 10, // vertical axis - can be a number or a string indicating unity. eg: '2em'
        },
        duration: 3000,
        close: false,
    }).showToast();
}

// Example usage
function debounce(cb, wait = 400) {
    let timeout;
    return function (...args) {
        const context = this;
        clearTimeout(timeout);
        timeout = setTimeout(() => cb.apply(context, args), wait);
    };
}

function getUniqueInputElements(formSelector) {
    const fieldMap = {};

    $(formSelector)
        .find("input[name]")
        .each(function () {
            if (!fieldMap[this.name]) {
                fieldMap[this.name] = this;
            }
        });

    return Object.values(fieldMap);
}

function formatNumberWithSeparator(number, separator = ".") {
    if (typeof number !== "number") {
        if (isNaN(number)) return "-";
        number = parseFloat(number);
    }

    return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, separator);
}

function copyText(value) {
    navigator.clipboard
        .writeText(value)
        .then(() => {
            console.log("Đã copy:", value);
        })
        .catch((err) => {
            console.error("Lỗi khi copy:", err);
        });
}

const numberToCurrency = function (number, unit = null) {
    if (number === 0 || number === "0" || number === "") {
        return "-";
    }

    let formatter = new Intl.NumberFormat("en-US", {
        minimumFractionDigits: 1,
        maximumFractionDigits: 2,
    });

    let formatted = formatter.format(number);
    if (unit === "%") return formatted + "% ";

    if (unit === "đ") return "₫ " + formatted;
    return formatted;
};

function formDataToNestedJson(formData) {
    const obj = {};

    formData.forEach((value, fullKey) => {
        // Check if key ends with [] => it's an array
        const isArray = fullKey.endsWith("[]");
        const cleanKey = isArray ? fullKey.slice(0, -2) : fullKey;

        const keys = cleanKey.split(/\[|\]/).filter((k) => k); // tách các phần tử lồng nhau

        let current = obj;
        for (let i = 0; i < keys.length; i++) {
            const key = keys[i];

            // Nếu là key cuối
            if (i === keys.length - 1) {
                if (isArray || current[key] !== undefined) {
                    if (!Array.isArray(current[key])) {
                        current[key] = current[key] !== undefined ? [current[key]] : [];
                    }
                    current[key].push(value);
                } else {
                    current[key] = value;
                }
            } else {
                if (!current[key]) {
                    current[key] = {};
                }
                current = current[key];
            }
        }
    });

    return obj;
}

function getUserInfo() {
    let userInfo = localStorage.getItem("user_info");
    try {
        const user = JSON.parse(userInfo);
        if (user && user?.role_name) {
            userInfo = user;
        }
    } catch (e) {
        console.error("Error parsing user info from localStorage:", e);
        return null;
    }
    return userInfo;
}

const numberFormat = function (field, delimiterSymbol = ".", decimalSymbol = ",") {
    if (typeof field !== "string") {
        throw new TypeError("Field selector must be a string.");
    }

    const cleaveNumber = document.querySelectorAll(field);
    cleaveNumber.forEach(function (field) {
        new Cleave(field, { numeral: true, numeralDecimalMark: decimalSymbol, delimiter: delimiterSymbol });
    });
};

const numberFormatToDisplayNumberPerCurrency = function (number, unit = null, minimumFractionDigits = 1, maximumFractionDigits = 2) {
    if (number === 0 || number === "0" || number === "") {
        return "-";
    }

    // Bước 1: Làm sạch chuỗi (nếu là string)
    if (typeof number === "string") {
        // Loại bỏ mọi ký tự không phải số, dấu trừ, hoặc dấu chấm
        number = number.replace(/[^0-9.-]+/g, "");
    }

    const num = parseFloat(number);
    if (isNaN(num)) return "-";

    // Làm tròn đến 2 chữ số thập phân
    let rounded = Math.round(num * 100) / 100;
    const isInteger = rounded % 1 === 0;

    let formatted;

    if (unit) {
        formatted = isInteger ? rounded.toLocaleString("en-US", { maximumFractionDigits: 0 }) : rounded.toLocaleString("en-US", { minimumFractionDigits, maximumFractionDigits });

        return formatted + " " + unit;
    }

    formatted = isInteger ? rounded.toLocaleString("en-US", { maximumFractionDigits: 0 }) : rounded.toLocaleString("en-US", { minimumFractionDigits: 1, maximumFractionDigits: 2 });

    return formatted;
};
