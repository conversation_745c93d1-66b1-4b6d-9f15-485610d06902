const getCookie = function (name) {
    let cookieValue = document.cookie.replace(/(?:(?:^|.*;\s*)username\s*\=\s*([^;]*).*$)|^.*$/, "$1");

    if (document.cookie && document.cookie !== "") {
        var cookies = document.cookie.split(";");
        for (var i = 0; i < cookies.length; i++) {
            var cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === name + "=") {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
};
axios.defaults.headers.common["X-CSRF-Token"] = getCookie("csrf_");

$.ajaxSetup({
    headers: {
        "X-CSRF-Token": getCookie("csrf_"),
    },
});
