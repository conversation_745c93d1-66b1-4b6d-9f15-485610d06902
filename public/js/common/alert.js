const Alert = {
    loading: ({ title, text, icon }, type) => {
        if (type === "lock") {
            Swal.fire({
                title: title,
                text: text,
                icon: icon,
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                },
            });
        } else {
            Swal.fire({
                title: title,
                text: text,
                icon: icon,
                timer: 2500,
            });
        }
    },
    error: function (message, useHtml = false) {
        Swal.fire({
            toast: true,
            position: "top-end",
            icon: "error",
            ...(useHtml ? { title: message } : { html: message }),
            showConfirmButton: !1,
            timer: 3500,
            showCloseButton: !0,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.onmouseenter = Swal.stopTimer;
                toast.onmouseleave = Swal.resumeTimer;
            },
        });
    },
    success: function (message) {
        Swal.fire({
            position: "center",
            icon: "success",
            title: message,
            showConfirmButton: !1,
            timer: 1500,
            showCloseButton: !0,
        });
    },
    warning: function (message) {
        Swal.fire({
            position: "top-end",
            icon: "warning",
            title: message,
            showConfirmButton: !1,
            timer: 1500,
            showCloseButton: !0,
        });
    },
    successCenter: function (message = "Success!") {
        Swal.fire({
            position: "center",
            icon: "success",
            title: message,
            showConfirmButton: false,
            timer: 1000,
            showCloseButton: true,
        });
    },
    confirm: function (target, callback = {}, title = "Are you sure?", confirmButtonText = "Yes", cancelButtonText = "No") {
        Swal.fire({
            title,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            cancelButtonText,
            confirmButtonText,
        }).then((result) => {
            if (result.isConfirmed) {
                callback(target);
            }
        });
    },
    confirmWithInput: function ({
        target,
        title = "Are you sure?",
        inputType = "text",
        inputPlaceholder = "Type your message here...",
        callback = {},
        confirmButtonText = "Yes",
        cancelButtonText = "No",
        inputValidator = function (value) {},
    }) {
        Swal.fire({
            title,
            input: inputType,
            inputPlaceholder,
            inputValidator,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            cancelButtonText,
            confirmButtonText,
        }).then((result) => {
            if (result.isConfirmed) {
                callback(target, result);
            }
        });
    },
    confirmWithInputs: function ({
        target,
        title = "Are you sure?",
        htmlInputs,
        inputPlaceholder = "Type your message here...",
        preConfirm = {},
        callback = {},
        willOpen = {},
        confirmButtonText = "Yes",
        cancelButtonText = "No",
        inputValidator = function (value) {},
    }) {
        Swal.fire({
            title,
            html: htmlInputs,
            inputPlaceholder,
            inputValidator,
            preConfirm,
            icon: "warning",
            focusConfirm: false,
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            cancelButtonText,
            confirmButtonText,
            willOpen,
        }).then((result) => {
            if (result.isConfirmed) {
                callback(target, result);
            }
        });
    },
};
