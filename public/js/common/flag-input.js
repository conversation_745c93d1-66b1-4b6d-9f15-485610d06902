/*
Template Name: Velzon - Admin & Dashboard Template
Author: Themesbrand
Website: https://Themesbrand.com/
Contact: <EMAIL>
File: flag input Js File
*/

(function () {
    "use strict";

    const API_URL = "/static/js/json/";
    let countryListData = [];

    // Load JSON data
    function getJSON(jsonUrl, callback) {
        const xhr = new XMLHttpRequest();
        xhr.open("GET", API_URL + jsonUrl, true);
        xhr.responseType = "json";

        xhr.onload = function () {
            if (xhr.status === 200) {
                callback(null, xhr.response);
            } else {
                callback(xhr.status, xhr.response);
            }
        };

        xhr.send();
    }

    // Generate HTML for one country item
    function createCountryItem(country) {
        return `
            <li class="dropdown-item d-flex">
                <div class="flex-shrink-0 me-2">
                    <img src="${window.location.origin}/${country.flagImg}" alt="country flag" class="options-flagimg" height="20">
                </div>
                <div class="flex-grow-1">
                    <div class="d-flex">
                        <div class="country-name me-1">${country.countryName}</div>
                        <span class="countrylist-codeno text-muted">${country.countryCode}</span>
                    </div>
                </div>
            </li>`;
    }

    // Load data to elements
    function loadCountryListData(data) {
        const inputElements = document.querySelectorAll("[data-input-flag]");
        const itemsHTML = data.map(createCountryItem).join("");

        inputElements.forEach((element) => {
            const listContainer = element.querySelector(".dropdown-menu-list");
            listContainer.innerHTML = itemsHTML;
            setupCountryListEvent(element);
        });
    }

    // Set up event for dropdown list
    function setupCountryListEvent(wrapper) {
        const currentImg = wrapper.querySelector(".country-flagimg")?.getAttribute("src");

        const dropdownItems = wrapper.querySelectorAll(".dropdown-menu li");
        const toggleBtn = wrapper.querySelector(".dropdown-toggle"); // <- chọn đúng button

        dropdownItems.forEach((item) => {
            const itemImg = item.querySelector(".options-flagimg").getAttribute("src");

            item.addEventListener("click", () => {
                const code = item.querySelector(".countrylist-codeno").innerText;

                if (toggleBtn) {
                    const imgEl = toggleBtn.querySelector("img");
                    const spanEl = toggleBtn.querySelector("span");

                    if (imgEl) imgEl.setAttribute("src", itemImg);
                    if (spanEl) spanEl.innerText = code;
                }
            });

            if (currentImg === itemImg) {
                item.classList.add("active");
            }
        });

        setupFlagImgWithName();
        setupFlagNameOnly();
    }

    // Handle [data-option-flag-img-name]
    function setupFlagImgWithName() {
        document.querySelectorAll("[data-option-flag-img-name]").forEach((container) => {
            const flagInput = container.querySelector(".flag-input");
            const bgImage = getComputedStyle(flagInput).backgroundImage;
            const currentFlagImg = bgImage.slice(bgImage.indexOf("/as") + 1, bgImage.lastIndexOf('"'));

            container.querySelectorAll(".dropdown-menu li").forEach((item) => {
                const itemImg = item.querySelector(".options-flagimg").getAttribute("src");

                item.addEventListener("click", () => {
                    const name = item.querySelector(".country-name").innerText;
                    flagInput.style.backgroundImage = `url(${itemImg})`;
                    flagInput.value = name;
                });

                if (currentFlagImg === itemImg) {
                    item.classList.add("active");
                    flagInput.value = item.querySelector(".country-name").innerText;
                }
            });
        });
    }

    // Handle [data-option-flag-name]
    function setupFlagNameOnly() {
        document.querySelectorAll("[data-option-flag-name]").forEach((container) => {
            const flagInput = container.querySelector(".flag-input");
            const currentName = flagInput.value;

            container.querySelectorAll(".dropdown-menu li").forEach((item) => {
                const itemName = item.querySelector(".country-name").innerText;

                item.addEventListener("click", () => {
                    flagInput.value = itemName;
                });

                if (currentName === itemName) {
                    item.classList.add("active");
                    flagInput.value = itemName;
                }
            });
        });
    }

    // Search filter
    function setupSearchBar() {
        document.querySelectorAll("[data-input-flag]").forEach((wrapper) => {
            const searchInput = wrapper.querySelector(".search-countryList");

            if (!searchInput) return;

            searchInput.addEventListener("keyup", () => {
                const query = searchInput.value.toLowerCase();
                const filtered = countryListData.filter((item) => item.countryName.toLowerCase().includes(query) || item.countryCode.includes(query));

                const filteredHTML = filtered.map(createCountryItem).join("");
                wrapper.querySelector(".dropdown-menu-list").innerHTML = filteredHTML;

                setupCountryListEvent(wrapper);
            });
        });
    }

    // Initialize
    function init() {
        getJSON("country-list.json", (err, data) => {
            if (err) {
                console.error("Something went wrong:", err);
            } else {
                countryListData = data;
                loadCountryListData(data);
                setupSearchBar();
            }
        });
    }

    init();
})();
