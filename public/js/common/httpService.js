import { getCookie } from "/static/js/common/helpers.js";
const baseURL = window.location.origin;
export const API_URL = {
    BUDGET: `${baseURL}/dsp/facebook/api/budget-schedules`,
    CAMP: `${baseURL}/dsp/facebook/api/campaigns`,
    ADS: `${baseURL}/dsp/facebook/api/ads`,
    LIST_USER: `${baseURL}/api/admins/users/list`,
    DETAIL_USER: `${baseURL}/api/admins/users/user`,
    GET_DETAILS_CAMP: `${baseURL}/dsp/facebook/api/campaigns/details`,
    LIST_CATALOGUE: `${baseURL}/dsp/facebook/api/catalogue/list`,
    LIST_CATALOGUE_ECOM: `${baseURL}/dsp/facebook/api/catalogue/list-collaborative`,
    LIST_CATALOGUE_WEB: `${baseURL}/dsp/facebook/api/catalogue/list-web`,
    API_END_POINT: `${baseURL}/dsp/facebook/api/campaigns/approve`,
};

export const TIKTOK_API_URL = {
    CAMPS: `${baseURL}/dsp/tiktok/api/campaign/list-table`,
    ADGROUPS: `${baseURL}/dsp/tiktok/api/adgroup/list-table`,
    ADS: `${baseURL}/dsp/tiktok/api/ad/list-table`,
};

export async function requestApi(method, url, payload, options) {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: url,
            method: method,
            contentType: "application/json",
            headers: {
                "X-CSRF-Token": getCookie("csrf_"),
            },
            ...options,
            data: JSON.stringify(payload),
        })
            .done((data) => resolve(data)) // Trả về dữ liệu nếu thành công
            .fail((jqXHR) => {
                console.log("error!");
                const errorResponse = jqXHR.responseJSON || {
                    status: jqXHR.status,
                    message: jqXHR.statusText,
                };
                reject(errorResponse); // Trả về responseJSON nếu lỗi
            });
    });
}
$(document).ready(() => {
    $.ajaxSetup({
        headers: {
            "X-CSRF-Token": getCookie("csrf_"),
        },
        contentType: "application/json",
        dataType: "json",
    });
});
