import Alert from "/static/js/components/alert.js";
import { getCookie } from "/static/js/common/helpers.js";

const API_BASE_URL = document.querySelector('meta[name="api-base-url"]')?.getAttribute("content") || "";

function handleErrorResponse(responseData) {
  if (responseData?.details?.msg && Array.isArray(responseData.details.msg)) {
    return responseData.details.msg.join("\n");
  }

  if (responseData?.message) {
    return responseData.message;
  }

  return "Đã xảy ra lỗi không xác định.";
}

function saveInfoUserToLocalStorage(user) {
  localStorage.setItem("user_info", JSON.stringify(user));
}

async function login(urlLogin, email, password) {
  try {
    const response = await fetch(urlLogin, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRF-Token": get<PERSON>ookie("csrf_"),
      },
      body: JSON.stringify({ email, password }),
    });

    const responseData = await tryParseJSON(response);

    if (!response.ok) {
      const errorMessage = handleErrorResponse(responseData);
      Alert.error(errorMessage);
      return;
    }

    const { access_token, user } = responseData?.data || {};

    if (!access_token?.token) {
      Alert.error("Không nhận được access token từ server.");
      return;
    }

    if (user?.role_name) {
      saveInfoUserToLocalStorage(user);
    }

    setTimeout(() => {
      handleLoginSuccess(responseData.data);
    }, 500);
  } catch (error) {
    console.error("Login error:", error);
    Alert.error("Có lỗi xảy ra khi đăng nhập. Vui lòng thử lại.");
  }
}

// Hàm phụ để parse JSON an toàn
async function tryParseJSON(response) {
  try {
    return await response.json();
  } catch {
    return null;
  }
}

// async function login(urlLogin, email, password) {
//     try {
//         const response = await fetch(urlLogin, {
//             method: "POST",
//             headers: {
//                 "Content-Type": "application/json",
//                 "X-CSRF-Token": getCookie("csrf_"),
//             },
//             body: JSON.stringify({ email, password }),
//         });

//         if (!response.ok) {
//             const errorData = await response.json().catch(() => null);
//             const errorMessage = handleErrorResponse(errorData);
//             Alert.error(errorMessage);
//             return;
//         }

//         const datas = await response.json();
//         console.log("Response Data:", datas);

//         if (datas?.data?.access_token?.token) {
//             if (datas.data?.user?.roleName) {
//                 saveInfoUserToLocalStorage(datas.data?.user);
//             }
//             handleLoginSuccess(datas.data);
//         } else {
//             Alert.error("Không nhận được access token từ server.");
//         }
//     } catch (error) {
//         Alert.error("Có lỗi xảy ra khi đăng nhập. Vui lòng thử lại.");
//     }
// }

function handleLoginSuccess(data) {
  const urlParams = new URLSearchParams(window.location.search);
  const redirectUrl = urlParams.get("redirect") || "/admins";
  window.location.href = redirectUrl;
}

function getHeaders() {
  return {
    "Content-Type": "application/json",
    "X-CSRF-Token": getCookie("csrf_"),
  };
}

function logout() {
  document.cookie = "brx_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; Secure; SameSite=Strict";
  localStorage.removeItem("access_token");
  localStorage.removeItem("access_token_exp");
  localStorage.removeItem("user_info");
  window.location.href = "/login";
}

export { login, handleErrorResponse, getHeaders };
