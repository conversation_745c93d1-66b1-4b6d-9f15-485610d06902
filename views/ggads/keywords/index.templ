package ads

import "googledsp/views/layouts"
import "googledsp/views/ggads/campaigns/components"
import cop "googledsp/views/ggads/components"

func getDataLayoutMaster(data *ListTableKeywordLayoutData) layouts.LayoutMasterData {
	return layouts.LayoutMasterData{}
}

func getDataLayoutTable(data *ListTableKeywordLayoutData) components.LayoutTableData {
	return components.LayoutTableData{}
}

templ ListTableKeyword(data *ListTableKeywordLayoutData) {
	{{
	dataLayoutMaster := getDataLayoutMaster(data)
	// pathBreadcrumb := getPathBreadcrumb()
	// dataLayoutTable := getDataLayoutTable(data)
	}}
	@layouts.Master(&dataLayoutMaster, &[]templ.Component{cssHeader()}, scriptAdgroup()) {
		// @layoutCops.ListBreadcrumdCpn("Ad Groups", pathBreadcrumb)
		if data != nil && data.FlashMsg != "" {
			@cop.FlashMsgCop(data.FlashMsg)
		}
		<div>Keyword</div>
	}
}

templ cssHeader() {
}

templ scriptAdgroup() {
}
