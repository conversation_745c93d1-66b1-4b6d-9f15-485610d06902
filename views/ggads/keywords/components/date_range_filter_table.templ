package components

templ DateRangeFilterTableCp() {
	<div class="input-group">
		<input
			type="text"
			class="form-control border-0 dash-filter-picker shadow"
			data-provider="flatpickr"
			data-range-date="true"
			data-date-format="d M, Y"
			data-deafult-date="01 Jan 2022 to 31 Jan 2022"
			readonly="readonly"
			placeholder="Select date"
			id="filter-search-datetime"
		/>
		<div class="input-group-text bg-primary border-primary text-white">
			<i class="ri-calendar-2-line"></i>
		</div>
	</div>
}
