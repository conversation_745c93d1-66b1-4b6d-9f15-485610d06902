package components

templ HtmlTemplateTable() {
	<template id="titleFootTableHtml">
		<th colspan="3" class="dtfc-fixed-left" style="position: sticky;left: 0;background-color: white;">
			Results from { `{rowCount} {title}` }
		</th>
	</template>
	<template id="nullFootTableHtml">
		<th class="align-top">
			<div class=" d-flex flex-column align-items-end fw-normal">
				<span class="text-dark">-</span>
			</div>
		</th>
	</template>
	<template id="totalFootTableHtml">
		<th class="align-top">
			<div class=" d-flex flex-column align-items-end fw-normal">
				<span class="text-dark">{ `{value}` }</span>
				<span class="text-muted fs-10">{ `{titleTotal}` }</span>
			</div>
		</th>
	</template>
	// Template for cell in table
	<template id="bugetCellTable">
		<div class="flex-grow-1 ms-3 text-right">
			<h5 class="mb-1 fs-12 text-primary">{ `{value}` }</h5>
			<p class="mb-0 fs-10 text-muted">{ `{label}` }</p>
		</div>
	</template>
	<template id="numberCellTable">
		<div class="text-end">
			<span>{ `{value}` }</span>
		</div>
	</template>
	<template id="rawValueCellTable">
		<div class="text-start">
			<span>{ `{value}` }</span>
		</div>
	</template>
	// Keyword Table
	<template id="bidCellTable">
		<div class="flex-grow-1 ms-3 text-right">
			<h5 class="mb-1 fs-12">{ `{value}` }</h5>
			<p class="mb-0 fs-10 text-muted">{ `{label}` }</p>
		</div>
	</template>
	<template id="resultAndResultRateCellTable">
		<div class="flex-grow-1 ms-3 text-right">
			<h5 class="mb-1 fs-12">{ `{value}` }</h5>
			<p class="mb-0 fs-10 text-muted">{ `{label}` }</p>
		</div>
	</template>
}
