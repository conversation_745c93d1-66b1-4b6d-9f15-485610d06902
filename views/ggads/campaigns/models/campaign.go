package models

import (
	"fmt"
	"time"
)

// Campaign represents a Google Ads campaign with performance metrics
type Campaign struct {
	ID           int64     `json:"id"`
	Name         string    `json:"name"`
	Status       string    `json:"status"`
	BudgetAmount float64   `json:"budget_amount"`
	BidStrategy  string    `json:"bid_strategy"`
	StartDate    time.Time `json:"start_date"`
	EndDate      *time.Time `json:"end_date,omitempty"`
	
	// Performance Metrics
	Impressions   int64   `json:"impressions"`
	Clicks        int64   `json:"clicks"`
	CTR           float64 `json:"ctr"`
	Cost          float64 `json:"cost"`
	Conversions   int64   `json:"conversions"`
	CPA           float64 `json:"cpa"`
	ROAS          float64 `json:"roas"`
	
	// Additional metrics
	AveragePosition float64 `json:"average_position"`
	QualityScore    float64 `json:"quality_score"`
	
	// Change indicators (for comparison with previous period)
	ImpressionsChange float64 `json:"impressions_change"`
	ClicksChange      float64 `json:"clicks_change"`
	CostChange        float64 `json:"cost_change"`
	ConversionsChange float64 `json:"conversions_change"`
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// CampaignSummary represents aggregated campaign metrics
type CampaignSummary struct {
	TotalCampaigns    int     `json:"total_campaigns"`
	ActiveCampaigns   int     `json:"active_campaigns"`
	PausedCampaigns   int     `json:"paused_campaigns"`
	TotalImpressions  int64   `json:"total_impressions"`
	TotalClicks       int64   `json:"total_clicks"`
	TotalCost         float64 `json:"total_cost"`
	TotalConversions  int64   `json:"total_conversions"`
	AverageCTR        float64 `json:"average_ctr"`
	AverageCPA        float64 `json:"average_cpa"`
	AverageROAS       float64 `json:"average_roas"`
}

// CampaignPerformanceData represents data for campaign performance charts
type CampaignPerformanceData struct {
	Labels      []string             `json:"labels"`
	Impressions []int64              `json:"impressions"`
	Clicks      []int64              `json:"clicks"`
	Cost        []float64            `json:"cost"`
	Conversions []int64              `json:"conversions"`
	Campaigns   []CampaignDataPoint  `json:"campaigns"`
}

// CampaignDataPoint represents a single data point for charts
type CampaignDataPoint struct {
	Date        string  `json:"date"`
	CampaignID  int64   `json:"campaign_id"`
	Impressions int64   `json:"impressions"`
	Clicks      int64   `json:"clicks"`
	Cost        float64 `json:"cost"`
	Conversions int64   `json:"conversions"`
}

// CampaignFilter represents filtering options for campaigns
type CampaignFilter struct {
	Status      []string   `json:"status"`
	DateFrom    *time.Time `json:"date_from"`
	DateTo      *time.Time `json:"date_to"`
	MinBudget   *float64   `json:"min_budget"`
	MaxBudget   *float64   `json:"max_budget"`
	BidStrategy []string   `json:"bid_strategy"`
	SearchTerm  string     `json:"search_term"`
	SortBy      string     `json:"sort_by"`
	SortOrder   string     `json:"sort_order"`
	Page        int        `json:"page"`
	Limit       int        `json:"limit"`
}

// GetStatusBadgeClass returns the CSS class for campaign status badge
func (c *Campaign) GetStatusBadgeClass() string {
	switch c.Status {
	case "ACTIVE", "ENABLED":
		return "bg-success-subtle text-success"
	case "PAUSED":
		return "bg-warning-subtle text-warning"
	case "REMOVED", "DELETED":
		return "bg-danger-subtle text-danger"
	default:
		return "bg-secondary-subtle text-secondary"
	}
}

// GetStatusDisplayName returns the display name for campaign status
func (c *Campaign) GetStatusDisplayName() string {
	switch c.Status {
	case "ENABLED":
		return "Active"
	case "PAUSED":
		return "Paused"
	case "REMOVED":
		return "Removed"
	default:
		return c.Status
	}
}

// GetCTRPercentage returns CTR as a formatted percentage
func (c *Campaign) GetCTRPercentage() string {
	if c.Impressions == 0 {
		return "0.00%"
	}
	return fmt.Sprintf("%.2f%%", c.CTR*100)
}

// GetFormattedCost returns cost formatted with currency
func (c *Campaign) GetFormattedCost() string {
	return fmt.Sprintf("₫%.0f", c.Cost)
}

// GetFormattedCPA returns CPA formatted with currency
func (c *Campaign) GetFormattedCPA() string {
	if c.CPA == 0 {
		return "₫0.00"
	}
	return fmt.Sprintf("₫%.2f", c.CPA)
}

// GetFormattedROAS returns ROAS formatted as multiplier
func (c *Campaign) GetFormattedROAS() string {
	if c.ROAS == 0 {
		return "0.0x"
	}
	return fmt.Sprintf("%.1fx", c.ROAS)
}

// GetChangeIndicator returns the change indicator class and icon
func (c *Campaign) GetChangeIndicator(changeValue float64) (string, string) {
	if changeValue > 0 {
		return "text-success", "ri-arrow-up-line"
	} else if changeValue < 0 {
		return "text-danger", "ri-arrow-down-line"
	}
	return "text-muted", "ri-subtract-line"
}

// GetFormattedChange returns formatted change percentage
func (c *Campaign) GetFormattedChange(changeValue float64) string {
	if changeValue == 0 {
		return "0.0%"
	}
	return fmt.Sprintf("%+.1f%%", changeValue)
}
