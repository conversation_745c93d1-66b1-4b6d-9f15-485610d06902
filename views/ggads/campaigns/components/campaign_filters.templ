package components

import "googledsp/views/ggads/campaigns/models"

templ CampaignFilters(filter *models.CampaignFilter) {
	<div class="card">
		<div class="card-body">
			<form 
				hx-get="/api/campaigns/filter"
				hx-target="#campaign-report-content"
				hx-swap="innerHTML"
				hx-trigger="change, submit"
			>
				<div class="row align-items-end">
					<!-- Date Range Filter -->
					<div class="col-md-3">
						<label class="form-label">Date Range</label>
						<div class="input-group">
							<input 
								type="text" 
								class="form-control" 
								id="campaign-daterange-picker"
								name="date_range"
								placeholder="Select date range"
							/>
							<span class="input-group-text">
								<i class="ri-calendar-line"></i>
							</span>
						</div>
					</div>
					
					<!-- Status Filter -->
					<div class="col-md-2">
						<label class="form-label">Status</label>
						<select class="form-select" name="status" multiple>
							<option value="">All Status</option>
							<option value="ACTIVE">Active</option>
							<option value="PAUSED">Paused</option>
							<option value="REMOVED">Removed</option>
						</select>
					</div>
					
					<!-- Bid Strategy Filter -->
					<div class="col-md-2">
						<label class="form-label">Bid Strategy</label>
						<select class="form-select" name="bid_strategy">
							<option value="">All Strategies</option>
							<option value="TARGET_CPA">Target CPA</option>
							<option value="TARGET_ROAS">Target ROAS</option>
							<option value="MAXIMIZE_CLICKS">Maximize Clicks</option>
							<option value="MAXIMIZE_CONVERSIONS">Maximize Conversions</option>
							<option value="MANUAL_CPC">Manual CPC</option>
						</select>
					</div>
					
					<!-- Budget Range -->
					<div class="col-md-2">
						<label class="form-label">Budget Range</label>
						<div class="input-group">
							<input 
								type="number" 
								class="form-control" 
								name="min_budget"
								placeholder="Min"
								step="1000"
							/>
							<span class="input-group-text">-</span>
							<input 
								type="number" 
								class="form-control" 
								name="max_budget"
								placeholder="Max"
								step="1000"
							/>
						</div>
					</div>
					
					<!-- Search -->
					<div class="col-md-2">
						<label class="form-label">Search</label>
						<div class="input-group">
							<input 
								type="text" 
								class="form-control" 
								name="search_term"
								placeholder="Campaign name..."
							/>
							<span class="input-group-text">
								<i class="ri-search-line"></i>
							</span>
						</div>
					</div>
					
					<!-- Actions -->
					<div class="col-md-1">
						<div class="d-flex gap-2">
							<button type="submit" class="btn btn-primary btn-sm">
								<i class="ri-search-line"></i>
							</button>
							<button 
								type="button" 
								class="btn btn-soft-secondary btn-sm"
								onclick="resetFilters()"
							>
								<i class="ri-refresh-line"></i>
							</button>
						</div>
					</div>
				</div>
				
				<!-- Advanced Filters (Collapsible) -->
				<div class="row mt-3">
					<div class="col-12">
						<button 
							class="btn btn-link p-0" 
							type="button" 
							data-bs-toggle="collapse" 
							data-bs-target="#advancedFilters"
						>
							<i class="ri-filter-3-line"></i> Advanced Filters
						</button>
					</div>
				</div>
				
				<div class="collapse" id="advancedFilters">
					<div class="row mt-3">
						<!-- Sort Options -->
						<div class="col-md-3">
							<label class="form-label">Sort By</label>
							<select class="form-select" name="sort_by">
								<option value="name">Campaign Name</option>
								<option value="impressions">Impressions</option>
								<option value="clicks">Clicks</option>
								<option value="cost">Cost</option>
								<option value="conversions">Conversions</option>
								<option value="ctr">CTR</option>
								<option value="cpa">CPA</option>
								<option value="roas">ROAS</option>
								<option value="created_at">Created Date</option>
							</select>
						</div>
						
						<div class="col-md-2">
							<label class="form-label">Order</label>
							<select class="form-select" name="sort_order">
								<option value="asc">Ascending</option>
								<option value="desc">Descending</option>
							</select>
						</div>
						
						<!-- Performance Filters -->
						<div class="col-md-2">
							<label class="form-label">Min CTR (%)</label>
							<input 
								type="number" 
								class="form-control" 
								name="min_ctr"
								placeholder="0.0"
								step="0.1"
								min="0"
							/>
						</div>
						
						<div class="col-md-2">
							<label class="form-label">Min ROAS</label>
							<input 
								type="number" 
								class="form-control" 
								name="min_roas"
								placeholder="0.0"
								step="0.1"
								min="0"
							/>
						</div>
						
						<div class="col-md-2">
							<label class="form-label">Max CPA (₫)</label>
							<input 
								type="number" 
								class="form-control" 
								name="max_cpa"
								placeholder="0"
								step="1000"
								min="0"
							/>
						</div>
						
						<!-- Results per page -->
						<div class="col-md-1">
							<label class="form-label">Per Page</label>
							<select class="form-select" name="limit">
								<option value="10">10</option>
								<option value="25" selected>25</option>
								<option value="50">50</option>
								<option value="100">100</option>
							</select>
						</div>
					</div>
				</div>
			</form>
		</div>
	</div>
}

templ CampaignFiltersScripts() {
	<script>
		function resetFilters() {
			// Reset all form fields
			document.querySelector('form').reset();
			
			// Reset date picker
			if (window.campaignDatePicker) {
				window.campaignDatePicker.clear();
			}
			
			// Trigger filter update
			htmx.trigger('form', 'submit');
		}
		
		// Initialize date range picker
		document.addEventListener('DOMContentLoaded', function() {
			if (typeof flatpickr !== 'undefined') {
				window.campaignDatePicker = flatpickr("#campaign-daterange-picker", {
					mode: "range",
					dateFormat: "Y-m-d",
					defaultDate: [new Date().fp_incr(-30), new Date()],
					onChange: function(selectedDates, dateStr, instance) {
						// Trigger HTMX request when date changes
						htmx.trigger('form', 'change');
					}
				});
			}
		});
		
		// Reinitialize after HTMX swaps
		document.addEventListener('htmx:afterSwap', function(evt) {
			if (evt.detail.target.id === 'campaign-report-content') {
				if (typeof flatpickr !== 'undefined' && !window.campaignDatePicker) {
					window.campaignDatePicker = flatpickr("#campaign-daterange-picker", {
						mode: "range",
						dateFormat: "Y-m-d",
						defaultDate: [new Date().fp_incr(-30), new Date()],
						onChange: function(selectedDates, dateStr, instance) {
							htmx.trigger('form', 'change');
						}
					});
				}
			}
		});
	</script>
}
