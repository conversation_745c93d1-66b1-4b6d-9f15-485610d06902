package components

templ HtmlTemplateTable() {
	<template id="titleFootTableHtml">
		<th colspan="3" class="dtfc-fixed-left" style="position: sticky;left: 0;background-color: white;">
			Results from { `{rowCount} {title}` }
		</th>
	</template>
	<template id="nullFootTableHtml">
		<th class="align-top">
			<div class=" d-flex flex-column align-items-end fw-normal">
				<span class="text-dark">-</span>
			</div>
		</th>
	</template>
	<template id="totalFootTableHtml">
		<th class="align-top">
			<div class=" d-flex flex-column align-items-end fw-normal">
				<span class="text-dark">{ `{value}` }</span>
				<span class="text-muted fs-10">{ `{titleTotal}` }</span>
			</div>
		</th>
	</template>
	// Template for cell in table
	<template id="bugetCellTable">
		<div class="flex-grow-1 ms-3 text-right">
			<h5 class="mb-1 fs-12 text-primary">{ `{value}` }</h5>
			<p class="mb-0 fs-10 text-muted">{ `{label}` }</p>
		</div>
	</template>
	<template id="numberCellTable">
		<div class="text-end">
			<span>{ `{value}` }</span>
		</div>
	</template>
	<template id="rawValueCellTable">
		<div class="text-start">
			<span>{ `{value}` }</span>
		</div>
	</template>
	// Adgroup Table
	<template id="bidCellTable">
		<div class="flex-grow-1 ms-3 text-right">
			<h5 class="mb-1 fs-12">{ `{value}` }</h5>
			<p class="mb-0 fs-10 text-muted">{ `{label}` }</p>
		</div>
	</template>
	<template id="resultAndResultRateCellTable">
		<div class="flex-grow-1 ms-3 text-right">
			<h5 class="mb-1 fs-12">{ `{value}` }</h5>
			<p class="mb-0 fs-10 text-muted">{ `{label}` }</p>
		</div>
	</template>
	// START Status Campaign
	<template id="statusCampaignData">
		// { templ.JSONString(enums.GMVCampaignStatus) }
	</template>
	<template id="statusActiveCampaign">
		<div>
			<span class="status-item-circle active me-1"></span>
			<span class="flex-1">Active</span>
			<span class="ms-1">
				<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" elementtiming="element-timing">
					<path
						fill="#9EE1DD"
						fill-rule="evenodd"
						d="M7.816.693a.667.667 0 0 1 .373 0l5.998 1.752c.284.083.48.344.48.64v3.593c0 4.063-2.6 7.67-6.456 8.955a.666.666 0 0 1-.421 0 9.44 9.44 0 0 1-6.457-8.957v-3.59c0-.297.196-.558.48-.64L7.816.692Z"
						clip-rule="evenodd"
						elementtiming="element-timing"
					></path>
					<path stroke="#002C2B" stroke-linecap="round" stroke-linejoin="round" d="M8 5.333v4h3.333" elementtiming="element-timing"></path>
				</svg>
			</span>
		</div>
	</template>
	<template id="statusInactiveCampaign">
		<div>
			<span class="status-item-circle inactive me-1"></span>
			<span class="flex-1">Inactive</span>
			<span class="ms-1 {display}">
				<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" elementtiming="element-timing">
					<path
						fill="#FAD253"
						fill-rule="evenodd"
						d="M7.816.693a.667.667 0 0 1 .373 0l5.998 1.752c.284.083.48.344.48.64v3.593c0 4.063-2.6 7.67-6.456 8.955a.666.666 0 0 1-.421 0 9.44 9.44 0 0 1-6.457-8.957v-3.59c0-.297.196-.558.48-.64L7.816.692Z"
						clip-rule="evenodd"
						elementtiming="element-timing"
					></path>
					<path
						stroke="#262627"
						stroke-linecap="round"
						stroke-linejoin="round"
						d="M9.834 6.136 6.063 9.907M6.063 6.136l3.77 3.771"
						elementtiming="element-timing"
					></path>
				</svg>
			</span>
		</div>
	</template>
	<template id="statusDeletedCampaign">
		<div>
			<span class="status-item-circle deleted me-1"></span>
			<span class="flex-1">Deleted</span>
		</div>
	</template>
	<template id="statusNotDeliveringCampaign">
		<div class="d-flex flex-column justify-content-center">
			<div>
				<span class="status-item-circle not-delivering me-1"></span>
				<span class="flex-1">Not delivering</span>
			</div>
			<span class="ms-1 text-muted fs-11">
				@templ.Raw("{label}")
			</span>
		</div>
	</template>
	// END Status Campaign
}
