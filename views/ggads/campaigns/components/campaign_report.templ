package components

import "googledsp/views/ggads/campaigns/models"
import "fmt"

type CampaignReportData struct {
	Campaigns       []*models.Campaign
	Summary         *models.CampaignSummary
	PerformanceData *models.CampaignPerformanceData
	Filter          *models.CampaignFilter
	ShowFilters     bool
	LastUpdated     string
}

templ CampaignReport(data *CampaignReportData) {
	<div id="campaign-report-content">
		<!-- Campaign Summary Cards -->
		<div class="row mb-4">
			<div class="col-12">
				@CampaignSummaryCards(data.Summary)
			</div>
		</div>
		
		<!-- Filters -->
		if data.ShowFilters {
			<div class="row mb-4">
				<div class="col-12">
					@CampaignFilters(data.Filter)
				</div>
			</div>
		}
		
		<!-- Performance Chart -->
		<div class="row mb-4">
			<div class="col-12">
				@CampaignPerformanceChart(data.PerformanceData)
			</div>
		</div>
		
		<!-- Campaign List Table -->
		<div class="row">
			<div class="col-12">
				@CampaignListTable(data.Campaigns)
			</div>
		</div>
		
		<!-- Last Updated Info -->
		<div class="row mt-3">
			<div class="col-12">
				<div class="text-muted text-center">
					<small>Last updated: { data.LastUpdated }</small>
					<button
						class="btn btn-link btn-sm p-0 ms-2"
						hx-get="/api/campaigns/refresh"
						hx-target="#campaign-report-content"
						hx-swap="innerHTML"
						hx-indicator="#loading-indicator"
					>
						<i class="ri-refresh-line"></i>
					</button>
				</div>
			</div>
		</div>
	</div>
	
	<!-- Loading Indicator -->
	<div id="loading-indicator" class="htmx-indicator">
		<div class="d-flex justify-content-center">
			<div class="spinner-border text-primary" role="status">
				<span class="visually-hidden">Loading...</span>
			</div>
		</div>
	</div>
}

templ CampaignSummaryCards(summary *models.CampaignSummary) {
	<div class="row">
		<div class="col-xl-2 col-md-4">
			<div class="card card-animate">
				<div class="card-body">
					<div class="d-flex align-items-center">
						<div class="flex-grow-1 overflow-hidden">
							<p class="text-uppercase fw-medium text-muted text-truncate mb-0">Total Campaigns</p>
						</div>
						<div class="flex-shrink-0">
							<h5 class="text-primary mb-0">
								<i class="ri-folder-chart-2-line fs-13 align-middle"></i>
							</h5>
						</div>
					</div>
					<div class="d-flex align-items-end justify-content-between mt-4">
						<div>
							<h4 class="fs-22 fw-semibold ff-secondary mb-4">
								<span class="counter-value">{ fmt.Sprintf("%d", summary.TotalCampaigns) }</span>
							</h4>
						</div>
						<div class="avatar-sm flex-shrink-0">
							<span class="avatar-title bg-soft-primary rounded fs-3">
								<i class="ri-folder-chart-2-line"></i>
							</span>
						</div>
					</div>
				</div>
			</div>
		</div>
		
		<div class="col-xl-2 col-md-4">
			<div class="card card-animate">
				<div class="card-body">
					<div class="d-flex align-items-center">
						<div class="flex-grow-1 overflow-hidden">
							<p class="text-uppercase fw-medium text-muted text-truncate mb-0">Active Campaigns</p>
						</div>
						<div class="flex-shrink-0">
							<h5 class="text-success mb-0">
								<i class="ri-play-circle-line fs-13 align-middle"></i>
							</h5>
						</div>
					</div>
					<div class="d-flex align-items-end justify-content-between mt-4">
						<div>
							<h4 class="fs-22 fw-semibold ff-secondary mb-4">
								<span class="counter-value">{ fmt.Sprintf("%d", summary.ActiveCampaigns) }</span>
							</h4>
						</div>
						<div class="avatar-sm flex-shrink-0">
							<span class="avatar-title bg-soft-success rounded fs-3">
								<i class="ri-play-circle-line"></i>
							</span>
						</div>
					</div>
				</div>
			</div>
		</div>
		
		<div class="col-xl-2 col-md-4">
			<div class="card card-animate">
				<div class="card-body">
					<div class="d-flex align-items-center">
						<div class="flex-grow-1 overflow-hidden">
							<p class="text-uppercase fw-medium text-muted text-truncate mb-0">Total Impressions</p>
						</div>
					</div>
					<div class="d-flex align-items-end justify-content-between mt-4">
						<div>
							<h4 class="fs-22 fw-semibold ff-secondary mb-4">
								<span class="counter-value">{ fmt.Sprintf("%d", summary.TotalImpressions) }</span>
							</h4>
						</div>
						<div class="avatar-sm flex-shrink-0">
							<span class="avatar-title bg-soft-info rounded fs-3">
								<i class="ri-eye-line"></i>
							</span>
						</div>
					</div>
				</div>
			</div>
		</div>
		
		<div class="col-xl-2 col-md-4">
			<div class="card card-animate">
				<div class="card-body">
					<div class="d-flex align-items-center">
						<div class="flex-grow-1 overflow-hidden">
							<p class="text-uppercase fw-medium text-muted text-truncate mb-0">Total Cost</p>
						</div>
					</div>
					<div class="d-flex align-items-end justify-content-between mt-4">
						<div>
							<h4 class="fs-22 fw-semibold ff-secondary mb-4">
								<span class="counter-value">₫{ fmt.Sprintf("%.0f", summary.TotalCost) }</span>
							</h4>
						</div>
						<div class="avatar-sm flex-shrink-0">
							<span class="avatar-title bg-soft-danger rounded fs-3">
								<i class="ri-money-dollar-circle-line"></i>
							</span>
						</div>
					</div>
				</div>
			</div>
		</div>
		
		<div class="col-xl-2 col-md-4">
			<div class="card card-animate">
				<div class="card-body">
					<div class="d-flex align-items-center">
						<div class="flex-grow-1 overflow-hidden">
							<p class="text-uppercase fw-medium text-muted text-truncate mb-0">Avg. CTR</p>
						</div>
					</div>
					<div class="d-flex align-items-end justify-content-between mt-4">
						<div>
							<h4 class="fs-22 fw-semibold ff-secondary mb-4">
								<span class="counter-value">{ fmt.Sprintf("%.2f%%", summary.AverageCTR*100) }</span>
							</h4>
						</div>
						<div class="avatar-sm flex-shrink-0">
							<span class="avatar-title bg-soft-warning rounded fs-3">
								<i class="ri-cursor-line"></i>
							</span>
						</div>
					</div>
				</div>
			</div>
		</div>
		
		<div class="col-xl-2 col-md-4">
			<div class="card card-animate">
				<div class="card-body">
					<div class="d-flex align-items-center">
						<div class="flex-grow-1 overflow-hidden">
							<p class="text-uppercase fw-medium text-muted text-truncate mb-0">Avg. ROAS</p>
						</div>
					</div>
					<div class="d-flex align-items-end justify-content-between mt-4">
						<div>
							<h4 class="fs-22 fw-semibold ff-secondary mb-4">
								<span class="counter-value">{ fmt.Sprintf("%.1fx", summary.AverageROAS) }</span>
							</h4>
						</div>
						<div class="avatar-sm flex-shrink-0">
							<span class="avatar-title bg-soft-success rounded fs-3">
								<i class="ri-line-chart-line"></i>
							</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
}
