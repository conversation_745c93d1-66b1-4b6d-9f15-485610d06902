package components

import "googledsp/views/ggads/campaigns/models"

templ CampaignPerformanceChart(data *models.CampaignPerformanceData) {
	<div class="card">
		<div class="card-header border-0 align-items-center d-flex">
			<h4 class="card-title mb-0 flex-grow-1">Campaign Performance Trends</h4>
			<div class="d-flex gap-1">
				<button type="button" class="btn btn-soft-secondary btn-sm">
					<i class="ri-file-list-3-line align-middle"></i> Generate Report
				</button>
				<div class="dropdown">
					<button class="btn btn-soft-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
						<i class="ri-more-2-line"></i>
					</button>
					<ul class="dropdown-menu">
						<li><a class="dropdown-item" href="#" onclick="changeChartType('line')">Line Chart</a></li>
						<li><a class="dropdown-item" href="#" onclick="changeChartType('bar')">Bar Chart</a></li>
						<li><a class="dropdown-item" href="#" onclick="changeChartType('area')">Area Chart</a></li>
						<li><hr class="dropdown-divider"/></li>
						<li><a class="dropdown-item" href="#" onclick="exportChart()">Export Chart</a></li>
					</ul>
				</div>
			</div>
		</div>
		
		<!-- Chart Metrics Summary -->
		<div class="card-header p-0 border-0 bg-soft-light">
			<div class="row g-0 text-center">
				<div class="col-6 col-sm-3">
					<div class="p-3 border border-dashed border-start-0">
						<h5 class="mb-1">
							<span class="counter-value" data-target="1275042">0</span>
						</h5>
						<p class="text-muted mb-0">Total Impressions</p>
					</div>
				</div>
				<div class="col-6 col-sm-3">
					<div class="p-3 border border-dashed border-start-0">
						<h5 class="mb-1">
							<span class="counter-value" data-target="15261">0</span>
						</h5>
						<p class="text-muted mb-0">Total Clicks</p>
					</div>
				</div>
				<div class="col-6 col-sm-3">
					<div class="p-3 border border-dashed border-start-0">
						<h5 class="mb-1">
							₫<span class="counter-value" data-target="1159000">0</span>
						</h5>
						<p class="text-muted mb-0">Total Cost</p>
					</div>
				</div>
				<div class="col-6 col-sm-3">
					<div class="p-3 border border-dashed border-start-0 border-end-0">
						<h5 class="mb-1">
							<span class="counter-value" data-target="412">0</span>
						</h5>
						<p class="text-muted mb-0">Total Conversions</p>
					</div>
				</div>
			</div>
		</div>
		
		<!-- Chart Container -->
		<div class="card-body p-0 pb-2">
			<div class="w-100">
				<div id="campaign_performance_chart" 
					data-colors='["--vz-primary", "--vz-success", "--vz-danger", "--vz-warning"]' 
					class="apex-charts" 
					dir="ltr"
					style="min-height: 350px;">
				</div>
			</div>
		</div>
		
		<!-- Chart Legend -->
		<div class="card-footer border-top-dashed">
			<div class="row text-center">
				<div class="col-sm-3">
					<div class="d-inline-flex align-items-center">
						<div class="bg-primary rounded me-2" style="width: 12px; height: 12px;"></div>
						<span class="text-muted">Impressions</span>
					</div>
				</div>
				<div class="col-sm-3">
					<div class="d-inline-flex align-items-center">
						<div class="bg-success rounded me-2" style="width: 12px; height: 12px;"></div>
						<span class="text-muted">Clicks</span>
					</div>
				</div>
				<div class="col-sm-3">
					<div class="d-inline-flex align-items-center">
						<div class="bg-danger rounded me-2" style="width: 12px; height: 12px;"></div>
						<span class="text-muted">Cost</span>
					</div>
				</div>
				<div class="col-sm-3">
					<div class="d-inline-flex align-items-center">
						<div class="bg-warning rounded me-2" style="width: 12px; height: 12px;"></div>
						<span class="text-muted">Conversions</span>
					</div>
				</div>
			</div>
		</div>
	</div>
}

templ CampaignChartScripts() {
	<script>
		let campaignChart;
		
		function initCampaignPerformanceChart() {
			// Sample data - in real implementation, this would come from the server
			const chartData = {
				labels: ['Jan 1', 'Jan 2', 'Jan 3', 'Jan 4', 'Jan 5', 'Jan 6', 'Jan 7'],
				series: [
					{
						name: 'Impressions',
						type: 'column',
						data: [180000, 195000, 210000, 185000, 220000, 205000, 190000]
					},
					{
						name: 'Clicks',
						type: 'line',
						data: [2100, 2300, 2500, 2200, 2600, 2400, 2200]
					},
					{
						name: 'Cost',
						type: 'line',
						data: [165000, 180000, 195000, 170000, 200000, 185000, 175000]
					},
					{
						name: 'Conversions',
						type: 'line',
						data: [58, 65, 72, 61, 78, 69, 63]
					}
				]
			};
			
			const options = {
				series: chartData.series,
				chart: {
					height: 350,
					type: 'line',
					stacked: false,
					toolbar: {
						show: true,
						tools: {
							download: true,
							selection: true,
							zoom: true,
							zoomin: true,
							zoomout: true,
							pan: true,
							reset: true
						}
					}
				},
				stroke: {
					width: [0, 2, 2, 2],
					curve: 'smooth'
				},
				plotOptions: {
					bar: {
						columnWidth: '50%'
					}
				},
				fill: {
					opacity: [0.85, 1, 1, 1],
					gradient: {
						inverseColors: false,
						shade: 'light',
						type: "vertical",
						opacityFrom: 0.85,
						opacityTo: 0.55,
						stops: [0, 100, 100, 100]
					}
				},
				labels: chartData.labels,
				markers: {
					size: 0
				},
				xaxis: {
					type: 'category'
				},
				yaxis: [
					{
						title: {
							text: 'Impressions',
						},
						seriesName: 'Impressions'
					},
					{
						opposite: true,
						title: {
							text: 'Clicks / Cost / Conversions'
						},
						seriesName: 'Clicks'
					}
				],
				tooltip: {
					shared: true,
					intersect: false,
					y: {
						formatter: function (y, { series, seriesIndex, dataPointIndex, w }) {
							if (seriesIndex === 0) {
								return y.toLocaleString() + " impressions";
							} else if (seriesIndex === 1) {
								return y.toLocaleString() + " clicks";
							} else if (seriesIndex === 2) {
								return "₫" + y.toLocaleString();
							} else if (seriesIndex === 3) {
								return y + " conversions";
							}
							return y;
						}
					}
				},
				colors: ['#038edc', '#51d28c', '#f34e4e', '#f7b84b'],
				legend: {
					show: false
				}
			};

			if (document.querySelector("#campaign_performance_chart")) {
				campaignChart = new ApexCharts(document.querySelector("#campaign_performance_chart"), options);
				campaignChart.render();
			}
		}
		
		function changeChartType(type) {
			if (campaignChart) {
				campaignChart.updateOptions({
					chart: {
						type: type
					}
				});
			}
		}
		
		function exportChart() {
			if (campaignChart) {
				campaignChart.dataURI().then(({ imgURI, blob }) => {
					const link = document.createElement('a');
					link.href = imgURI;
					link.download = 'campaign-performance-chart.png';
					link.click();
				});
			}
		}

		// Initialize chart when page loads
		document.addEventListener('DOMContentLoaded', function() {
			if (typeof ApexCharts !== 'undefined') {
				initCampaignPerformanceChart();
			}
		});

		// Reinitialize chart after HTMX swaps
		document.addEventListener('htmx:afterSwap', function(evt) {
			if (evt.detail.target.id === 'campaign-report-content') {
				setTimeout(() => {
					if (typeof ApexCharts !== 'undefined') {
						initCampaignPerformanceChart();
					}
				}, 100);
			}
		});
	</script>
}
