package components

import "googledsp/views/ggads/campaigns/models"
import "fmt"

templ CampaignListTable(campaigns []*models.Campaign) {
	<div class="card">
		<div class="card-header d-flex align-items-center">
			<h5 class="card-title mb-0 flex-grow-1">Campaign Performance</h5>
			<div class="flex-shrink-0">
				<div class="d-flex gap-2">
					<button
						type="button"
						class="btn btn-soft-primary btn-sm"
						hx-get="/api/campaigns/refresh"
						hx-target="#campaign-report-content"
						hx-swap="innerHTML"
					>
						<i class="ri-refresh-line"></i> Refresh
					</button>
					<button
						type="button"
						class="btn btn-soft-success btn-sm"
						onclick="exportCampaigns()"
					>
						<i class="ri-download-line"></i> Export
					</button>
					<button
						type="button"
						class="btn btn-primary btn-sm"
						data-bs-toggle="modal"
						data-bs-target="#createCampaignModal"
					>
						<i class="ri-add-line"></i> New Campaign
					</button>
				</div>
			</div>
		</div>
		<div class="card-body">
			<div class="table-responsive">
				<table class="table table-hover table-nowrap align-middle mb-0">
					<thead class="table-light">
						<tr>
							<th scope="col">
								<input type="checkbox" class="form-check-input" id="selectAll"/>
							</th>
							<th scope="col">Campaign</th>
							<th scope="col">Status</th>
							<th scope="col">Budget</th>
							<th scope="col">Bid Strategy</th>
							<th scope="col">Impressions</th>
							<th scope="col">Clicks</th>
							<th scope="col">CTR</th>
							<th scope="col">Cost</th>
							<th scope="col">Conversions</th>
							<th scope="col">CPA</th>
							<th scope="col">ROAS</th>
							<th scope="col">Actions</th>
						</tr>
					</thead>
					<tbody>
						if len(campaigns) == 0 {
							<tr>
								<td colspan="13" class="text-center py-4">
									<div class="d-flex flex-column align-items-center">
										<i class="ri-folder-open-line fs-1 text-muted mb-2"></i>
										<h6 class="text-muted">No campaigns found</h6>
										<p class="text-muted mb-0">Try adjusting your filters or create a new campaign</p>
									</div>
								</td>
							</tr>
						} else {
							for _, campaign := range campaigns {
								@CampaignTableRow(campaign)
							}
						}
					</tbody>
				</table>
			</div>
			<!-- Pagination -->
			if len(campaigns) > 0 {
				<div class="row mt-3">
					<div class="col-sm-6">
						<div class="text-muted">
							Showing { fmt.Sprintf("%d", len(campaigns)) } campaigns
						</div>
					</div>
					<div class="col-sm-6">
						<nav aria-label="Campaign pagination">
							<ul class="pagination pagination-sm justify-content-end mb-0">
								<li class="page-item">
									<a
										class="page-link"
										href="#"
										hx-get="/api/campaigns/list?page=1"
										hx-target="#campaign-report-content"
										hx-swap="innerHTML"
									>
										<i class="ri-arrow-left-line"></i>
									</a>
								</li>
								<li class="page-item active">
									<span class="page-link">1</span>
								</li>
								<li class="page-item">
									<a
										class="page-link"
										href="#"
										hx-get="/api/campaigns/list?page=2"
										hx-target="#campaign-report-content"
										hx-swap="innerHTML"
									>
										<i class="ri-arrow-right-line"></i>
									</a>
								</li>
							</ul>
						</nav>
					</div>
				</div>
			}
		</div>
	</div>
}

templ CampaignTableRow(campaign *models.Campaign) {
	<tr>
		<td>
			<input type="checkbox" class="form-check-input campaign-checkbox" value={ fmt.Sprintf("%d", campaign.ID) }/>
		</td>
		<td>
			<div class="d-flex align-items-center">
				<div class="flex-shrink-0 me-2">
					<div class="avatar-xs">
						<div class="avatar-title bg-soft-primary text-primary rounded-circle fs-13">
							<i class="ri-advertisement-line"></i>
						</div>
					</div>
				</div>
				<div class="flex-grow-1">
					<h6 class="mb-0">
						<a
							href={ templ.SafeURL(fmt.Sprintf("/dsp/googleads/campaigns/edit?id=%d", campaign.ID)) }
							class="text-decoration-none"
						>
							{ campaign.Name }
						</a>
					</h6>
					<p class="text-muted mb-0">ID: { fmt.Sprintf("%d", campaign.ID) }</p>
				</div>
			</div>
		</td>
		<td>
			<span class={ fmt.Sprintf("badge %s", campaign.GetStatusBadgeClass()) }>
				{ campaign.GetStatusDisplayName() }
			</span>
		</td>
		<td>
			<div class="d-flex flex-column">
				<span class="fw-medium">{ campaign.GetFormattedCost() }</span>
				<small class="text-muted">Daily</small>
			</div>
		</td>
		<td>
			<span class="badge bg-light text-dark">{ campaign.BidStrategy }</span>
		</td>
		<td>
			<div class="d-flex flex-column">
				<span class="fw-medium">{ fmt.Sprintf("%d", campaign.Impressions) }</span>
				if campaign.ImpressionsChange != 0 {
					{{
						changeClass, changeIcon := campaign.GetChangeIndicator(campaign.ImpressionsChange)
					}}
					<small class={ changeClass }>
						<i class={ changeIcon }></i>
						{ campaign.GetFormattedChange(campaign.ImpressionsChange) }
					</small>
				}
			</div>
		</td>
		<td>
			<div class="d-flex flex-column">
				<span class="fw-medium">{ fmt.Sprintf("%d", campaign.Clicks) }</span>
				if campaign.ClicksChange != 0 {
					{{
						changeClass, changeIcon := campaign.GetChangeIndicator(campaign.ClicksChange)
					}}
					<small class={ changeClass }>
						<i class={ changeIcon }></i>
						{ campaign.GetFormattedChange(campaign.ClicksChange) }
					</small>
				}
			</div>
		</td>
		<td>
			<span class="fw-medium">{ campaign.GetCTRPercentage() }</span>
		</td>
		<td>
			<div class="d-flex flex-column">
				<span class="fw-medium">{ campaign.GetFormattedCost() }</span>
				if campaign.CostChange != 0 {
					{{
						changeClass, changeIcon := campaign.GetChangeIndicator(campaign.CostChange)
					}}
					<small class={ changeClass }>
						<i class={ changeIcon }></i>
						{ campaign.GetFormattedChange(campaign.CostChange) }
					</small>
				}
			</div>
		</td>
		<td>
			<div class="d-flex flex-column">
				<span class="fw-medium">{ fmt.Sprintf("%d", campaign.Conversions) }</span>
				if campaign.ConversionsChange != 0 {
					{{
						changeClass, changeIcon := campaign.GetChangeIndicator(campaign.ConversionsChange)
					}}
					<small class={ changeClass }>
						<i class={ changeIcon }></i>
						{ campaign.GetFormattedChange(campaign.ConversionsChange) }
					</small>
				}
			</div>
		</td>
		<td>
			<span class="fw-medium">{ campaign.GetFormattedCPA() }</span>
		</td>
		<td>
			<span class="fw-medium text-success">{ campaign.GetFormattedROAS() }</span>
		</td>
		<td>
			<div class="dropdown">
				<button class="btn btn-soft-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
					<i class="ri-more-fill"></i>
				</button>
				<ul class="dropdown-menu">
					<li>
						<a class="dropdown-item" href={ templ.SafeURL(fmt.Sprintf("/dsp/googleads/campaigns/edit?id=%d", campaign.ID)) }>
							<i class="ri-pencil-line me-2"></i>Edit
						</a>
					</li>
					<li>
						<a class="dropdown-item" href="#" onclick={ templ.ComponentScript{Call: fmt.Sprintf("duplicateCampaign(%d)", campaign.ID)} }>
							<i class="ri-file-copy-line me-2"></i>Duplicate
						</a>
					</li>
					<li><hr class="dropdown-divider"/></li>
					<li>
						<a
							class="dropdown-item"
							href="#"
							onclick={ templ.ComponentScript{Call: fmt.Sprintf("toggleCampaignStatus(%d, '%s')", campaign.ID, campaign.Status)} }
						>
							<i class="ri-play-pause-line me-2"></i>
							if campaign.Status == "ACTIVE" {
								Pause
							} else {
								Activate
							}
						</a>
					</li>
					<li><hr class="dropdown-divider"/></li>
					<li>
						<a
							class="dropdown-item text-danger"
							href="#"
							onclick={ templ.ComponentScript{Call: fmt.Sprintf("deleteCampaign(%d)", campaign.ID)} }
						>
							<i class="ri-delete-bin-line me-2"></i>Delete
						</a>
					</li>
				</ul>
			</div>
		</td>
	</tr>
}
