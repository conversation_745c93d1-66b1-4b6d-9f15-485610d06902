package campaigns

import "googledsp/views/layouts"
import "googledsp/views/ggads/campaigns/components"
import "googledsp/views/ggads/campaigns/models"
import cop "googledsp/views/ggads/components"
import "time"

func getDataLayoutMaster(data *ListTableCampaignLayoutData) layouts.LayoutMasterData {
	return layouts.LayoutMasterData{}
}

func getDataLayoutTable(data *ListTableCampaignLayoutData) components.LayoutTableData {
	return components.LayoutTableData{}
}

func getMockCampaignReportData() *components.CampaignReportData {
	// Mock campaigns data
	campaigns := []*models.Campaign{
		{
			ID:                12345,
			Name:              "Summer Sale 2024",
			Status:            "ACTIVE",
			BudgetAmount:      50000,
			BidStrategy:       "TARGET_CPA",
			StartDate:         time.Now().AddDate(0, -1, 0),
			Impressions:       1275042,
			Clicks:            15261,
			CTR:               0.012,
			Cost:              1159000,
			Conversions:       412,
			CPA:               2815,
			ROAS:              4.2,
			ImpressionsChange: 12.5,
			ClicksChange:      8.3,
			CostChange:        -5.2,
			ConversionsChange: 15.7,
			CreatedAt:         time.Now().AddDate(0, -2, 0),
			UpdatedAt:         time.Now(),
		},
		{
			ID:                67890,
			Name:              "Holiday Promotion",
			Status:            "PAUSED",
			BudgetAmount:      75000,
			BidStrategy:       "TARGET_ROAS",
			StartDate:         time.Now().AddDate(0, -2, 0),
			Impressions:       892156,
			Clicks:            8921,
			CTR:               0.010,
			Cost:              892000,
			Conversions:       178,
			CPA:               5011,
			ROAS:              3.1,
			ImpressionsChange: -8.2,
			ClicksChange:      -12.1,
			CostChange:        -15.3,
			ConversionsChange: -22.4,
			CreatedAt:         time.Now().AddDate(0, -3, 0),
			UpdatedAt:         time.Now().AddDate(0, 0, -1),
		},
		{
			ID:                11111,
			Name:              "Back to School Campaign",
			Status:            "ACTIVE",
			BudgetAmount:      30000,
			BidStrategy:       "MAXIMIZE_CLICKS",
			StartDate:         time.Now().AddDate(0, 0, -15),
			Impressions:       456789,
			Clicks:            5678,
			CTR:               0.0124,
			Cost:              567800,
			Conversions:       89,
			CPA:               6380,
			ROAS:              3.8,
			ImpressionsChange: 25.3,
			ClicksChange:      18.7,
			CostChange:        12.4,
			ConversionsChange: 31.2,
			CreatedAt:         time.Now().AddDate(0, 0, -20),
			UpdatedAt:         time.Now(),
		},
	}

	// Mock summary data
	summary := &models.CampaignSummary{
		TotalCampaigns:   len(campaigns),
		ActiveCampaigns:  2,
		PausedCampaigns:  1,
		TotalImpressions: 2623987,
		TotalClicks:      29860,
		TotalCost:        2618800,
		TotalConversions: 679,
		AverageCTR:       0.0114,
		AverageCPA:       3856,
		AverageROAS:      3.7,
	}

	// Mock performance data
	performanceData := &models.CampaignPerformanceData{
		Labels:      []string{"Jan 1", "Jan 2", "Jan 3", "Jan 4", "Jan 5", "Jan 6", "Jan 7"},
		Impressions: []int64{180000, 195000, 210000, 185000, 220000, 205000, 190000},
		Clicks:      []int64{2100, 2300, 2500, 2200, 2600, 2400, 2200},
		Cost:        []float64{165000, 180000, 195000, 170000, 200000, 185000, 175000},
		Conversions: []int64{58, 65, 72, 61, 78, 69, 63},
	}

	// Mock filter
	filter := &models.CampaignFilter{
		Status:    []string{},
		SortBy:    "name",
		SortOrder: "asc",
		Page:      1,
		Limit:     25,
	}

	return &components.CampaignReportData{
		Campaigns:       campaigns,
		Summary:         summary,
		PerformanceData: performanceData,
		Filter:          filter,
		ShowFilters:     true,
		LastUpdated:     time.Now().Format("2006-01-02 15:04:05"),
	}
}

templ ListTableCampaign(data *ListTableCampaignLayoutData) {
	{{
	dataLayoutMaster := getDataLayoutMaster(data)
	reportData := getMockCampaignReportData()
	}}
	@layouts.Master(&dataLayoutMaster, &[]templ.Component{cssHeader()}, scriptCampaign()) {
		<div class="row">
			<div class="col-12">
				<div class="page-title-box d-sm-flex align-items-center justify-content-between bg-galaxy-transparent">
					<h4 class="mb-sm-0">Campaign Management</h4>
					<div class="page-title-right">
						<ol class="breadcrumb m-0">
							<li class="breadcrumb-item"><a href="javascript: void(0);">Google Ads</a></li>
							<li class="breadcrumb-item active">Campaigns</li>
						</ol>
					</div>
				</div>
			</div>
		</div>
		if data != nil && data.FlashMsg != "" {
			@cop.FlashMsgCop(data.FlashMsg)
		}
		<!-- Campaign Report Component -->
		@components.CampaignReport(reportData)
	}
}

templ cssHeader() {
	<link href="/static/themes/libs/flatpickr/flatpickr.min.css" rel="stylesheet" type="text/css"/>
	<style>
		.htmx-indicator {
			display: none;
		}
		.htmx-request .htmx-indicator {
			display: block;
		}
		.htmx-request.htmx-indicator {
			display: block;
		}
		.campaign-checkbox:checked {
			background-color: var(--vz-primary);
			border-color: var(--vz-primary);
		}
		.table-responsive {
			border-radius: 0.375rem;
		}
		.card-animate {
			transition: all 0.3s ease;
		}
		.card-animate:hover {
			transform: translateY(-2px);
			box-shadow: 0 4px 8px rgba(0,0,0,0.1);
		}
	</style>
}

templ scriptCampaign() {
	<script src="/static/themes/libs/apexcharts/apexcharts.min.js"></script>
	<script src="/static/themes/libs/flatpickr/flatpickr.min.js"></script>
	<script src="https://unpkg.com/htmx.org@1.9.10"></script>
	@components.CampaignFiltersScripts()
	@components.CampaignChartScripts()
	<script>
		// Campaign management functions
		function toggleCampaignStatus(campaignId, currentStatus) {
			const newStatus = currentStatus === 'ACTIVE' ? 'PAUSED' : 'ACTIVE';
			const action = newStatus === 'ACTIVE' ? 'activate' : 'pause';

			if (confirm(`Are you sure you want to ${action} this campaign?`)) {
				// Make HTMX request to update campaign status
				htmx.ajax('POST', `/api/campaigns/${campaignId}/status`, {
					values: { status: newStatus },
					target: '#campaign-report-content',
					swap: 'innerHTML'
				});
			}
		}

		function duplicateCampaign(campaignId) {
			if (confirm('Are you sure you want to duplicate this campaign?')) {
				htmx.ajax('POST', `/api/campaigns/${campaignId}/duplicate`, {
					target: '#campaign-report-content',
					swap: 'innerHTML'
				});
			}
		}

		function deleteCampaign(campaignId) {
			if (confirm('Are you sure you want to delete this campaign? This action cannot be undone.')) {
				htmx.ajax('DELETE', `/api/campaigns/${campaignId}`, {
					target: '#campaign-report-content',
					swap: 'innerHTML'
				});
			}
		}

		function exportCampaigns() {
			const selectedCampaigns = Array.from(document.querySelectorAll('.campaign-checkbox:checked'))
				.map(cb => cb.value);

			if (selectedCampaigns.length === 0) {
				alert('Please select at least one campaign to export.');
				return;
			}

			// Create export URL with selected campaign IDs
			const exportUrl = `/api/campaigns/export?ids=${selectedCampaigns.join(',')}`;
			window.open(exportUrl, '_blank');
		}

		// Select all campaigns functionality
		document.addEventListener('DOMContentLoaded', function() {
			const selectAllCheckbox = document.getElementById('selectAll');
			if (selectAllCheckbox) {
				selectAllCheckbox.addEventListener('change', function() {
					const campaignCheckboxes = document.querySelectorAll('.campaign-checkbox');
					campaignCheckboxes.forEach(cb => {
						cb.checked = this.checked;
					});
				});
			}
		});

		// Update select all checkbox when individual checkboxes change
		document.addEventListener('change', function(e) {
			if (e.target.classList.contains('campaign-checkbox')) {
				const selectAllCheckbox = document.getElementById('selectAll');
				const campaignCheckboxes = document.querySelectorAll('.campaign-checkbox');
				const checkedCount = document.querySelectorAll('.campaign-checkbox:checked').length;

				if (selectAllCheckbox) {
					selectAllCheckbox.checked = checkedCount === campaignCheckboxes.length;
					selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < campaignCheckboxes.length;
				}
			}
		});
	</script>
}
