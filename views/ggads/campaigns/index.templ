package campaigns

import (
	"googledsp/views/ggads/campaigns/components"
	"googledsp/views/layouts"

	cop "googledsp/views/ggads/components"

	"github.com/dev-networldasia/dspgos/gos/templates"
	campCp "googledsp/views/ggads/campaigns/components"
)

func getDataLayoutMaster(_ *ListTableCampaignLayoutData) layouts.LayoutMasterData {
	return layouts.LayoutMasterData{}
}

func getDataLayoutTable(_ *ListTableCampaignLayoutData) components.LayoutTableData {
	return components.LayoutTableData{}
}

func getPathBreadcrumb() []cop.PathBreadcrumb {
	return []cop.PathBreadcrumb{
		{Title: "Campaigns", DataKey: "t-campaigns", Url: "/dsp/googleads/campaigns/list"},
		{Title: "List", DataKey: "t-campaigns", Url: "/dsp/googleads/campaigns/list#"},
	}
}

templ ListTableCampaign(data *ListTableCampaignLayoutData) {
	{{
		dataLayoutMaster := getDataLayoutMaster(data)
		pathBreadcrumb := []cop.PathBreadcrumb{
			{Title: "Campaigns", DataKey: "t-campaigns", Url: "/dsp/googleads/campaigns/list"},
			{Title: "List", DataKey: "t-campaigns", Url: "/dsp/googleads/campaigns/list#"},
		}
		// dataLayoutTable := getDataLayoutTable(data)
	}}
	@layouts.Master(&dataLayoutMaster, &[]templ.Component{cssHeader()}, scriptCampaign()) {
		@cop.ListBreadcrumdCpn("Campaigns", pathBreadcrumb)
		if data != nil && data.FlashMsg != "" {
			@cop.FlashMsgCop(data.FlashMsg)
		}
		<div class="row">
			<div class="col-lg-12">
				<div class="card">
					<div class="card-header d-flex justify-content-between">
						@campCp.FilterHeaderTableCp(&dataLayoutMaster)
					</div>
					<div class="card-body pt-0">
						@campCp.ListTableCp()
					</div>
				</div>
			</div>
		</div>
	}
}

templ cssHeader() {
	<link href={ templates.AssetURL("/static/themes/libs/datatables/datatables.min.css") } rel="stylesheet" type="text/css"/>
}

templ scriptCampaign() {
	<script type="text/javascript" src={ templates.AssetURL("/static/js/common/alert.js") }></script>
	<script type="text/javascript" src={ templates.AssetURL("/static/themes/libs/moment/moment.min.js") }></script>
	<script type="text/javascript" src={ templates.AssetURL("/static/themes/libs/daterangepicker/daterangepicker.min.js") }></script>
	<script type="text/javascript" src={ templates.AssetURL("/static/themes/libs/datatables/datatables.min.js") } defer></script>
	<script type="text/javascript" src={ templates.AssetURL("/static/themes/libs/mathjs/math.min.js") } defer></script>
}
