package campaigns

import (
	"googledsp/views/ggads/campaigns/components"
	cop "googledsp/views/ggads/components"
	"googledsp/views/layouts"
)

func getDataLayoutMaster(_ *ListTableCampaignLayoutData) layouts.LayoutMasterData {
	return layouts.LayoutMasterData{}
}

func getDataLayoutTable(_ *ListTableCampaignLayoutData) components.LayoutTableData {
	return components.LayoutTableData{}
}

templ ListTableCampaign(data *ListTableCampaignLayoutData) {
	{{
	dataLayoutMaster := getDataLayoutMaster(data)
	}}
	@layouts.Master(&dataLayoutMaster, &[]templ.Component{cssHeader()}, scriptCampaign()) {
		<div class="row">
			<div class="col-12">
				<div class="page-title-box d-sm-flex align-items-center justify-content-between bg-galaxy-transparent">
					<h4 class="mb-sm-0">Campaign Management</h4>
					<div class="page-title-right">
						<ol class="breadcrumb m-0">
							<li class="breadcrumb-item"><a href="javascript: void(0);">Google Ads</a></li>
							<li class="breadcrumb-item active">Campaigns</li>
						</ol>
					</div>
				</div>
			</div>
		</div>
		if data != nil && data.FlashMsg != "" {
			@cop.FlashMsgCop(data.FlashMsg)
		}
		<!-- Campaign List Table -->
		<div class="row">
			<div class="col-12">
				<div class="card">
					<div class="card-header">
						<h4 class="card-title mb-0">Campaigns</h4>
					</div>
					<div class="card-body">
						<div id="campaignTable">
							<!-- DataTable will be loaded here via AJAX -->
							<div class="text-center py-4">
								<div class="spinner-border text-primary" role="status">
									<span class="visually-hidden">Loading...</span>
								</div>
								<p class="mt-2">Loading campaigns...</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	}
}

templ cssHeader() {
	<link href="/static/themes/libs/flatpickr/flatpickr.min.css" rel="stylesheet" type="text/css"/>
	<link href="/static/themes/libs/datatables.net-bs5/css/dataTables.bootstrap5.min.css" rel="stylesheet" type="text/css"/>
	<link href="/static/themes/libs/datatables.net-responsive-bs5/css/responsive.bootstrap5.min.css" rel="stylesheet" type="text/css"/>
}

templ scriptCampaign() {
	<script src="/static/themes/libs/datatables.net/js/jquery.dataTables.min.js"></script>
	<script src="/static/themes/libs/datatables.net-bs5/js/dataTables.bootstrap5.min.js"></script>
	<script src="/static/themes/libs/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
	<script src="/static/themes/libs/datatables.net-responsive-bs5/js/responsive.bootstrap5.min.js"></script>
	<script>
		$(document).ready(function() {
			// Initialize DataTable for campaigns
			$('#campaignTable').html(`
				<table id="campaignDataTable" class="table table-bordered dt-responsive nowrap table-striped align-middle" style="width:100%">
					<thead>
						<tr>
							<th>Campaign Name</th>
							<th>Status</th>
							<th>Budget</th>
							<th>Impressions</th>
							<th>Clicks</th>
							<th>CTR</th>
							<th>Cost</th>
							<th>Conversions</th>
							<th>Actions</th>
						</tr>
					</thead>
					<tbody></tbody>
				</table>
			`);

			var table = $('#campaignDataTable').DataTable({
				processing: true,
				serverSide: true,
				ajax: {
					url: '/dsp/googleads/api/campaigns/list-table',
					type: 'POST',
					contentType: 'application/json',
					data: function(d) {
						return JSON.stringify({
							draw: d.draw,
							start: d.start,
							length: d.length,
							search: d.search.value || null,
							order: d.order.map(function(order) {
								return {
									column: order.column,
									dir: order.dir
								};
							})
						});
					},
					error: function(xhr, error, code) {
						console.error('DataTable AJAX error:', error, code);
						console.error('Response:', xhr.responseText);

						// Show user-friendly error message
						$('#campaignTable').html(`
							<div class="alert alert-danger" role="alert">
								<h4 class="alert-heading">Error Loading Campaigns</h4>
								<p>There was an error loading the campaign data. Please try refreshing the page.</p>
								<hr>
								<p class="mb-0">Error: ${error} (${code})</p>
							</div>
						`);
					}
				},
				columns: [
					{ data: 'name', name: 'name' },
					{
						data: 'status',
						name: 'status',
						render: function(data, type, row) {
							const badgeClass = data === 'ACTIVE' ? 'bg-success' :
											 data === 'PAUSED' ? 'bg-warning' : 'bg-secondary';
							return `<span class="badge ${badgeClass}">${data}</span>`;
						}
					},
					{
						data: 'budget',
						name: 'budget',
						render: function(data, type, row) {
							return data ? '₫' + parseFloat(data).toLocaleString() : '₫0';
						}
					},
					{
						data: 'impressions',
						name: 'impressions',
						render: function(data, type, row) {
							return data ? parseInt(data).toLocaleString() : '0';
						}
					},
					{
						data: 'clicks',
						name: 'clicks',
						render: function(data, type, row) {
							return data ? parseInt(data).toLocaleString() : '0';
						}
					},
					{
						data: 'ctr',
						name: 'ctr',
						render: function(data, type, row) {
							return data ? (parseFloat(data) * 100).toFixed(2) + '%' : '0.00%';
						}
					},
					{
						data: 'cost',
						name: 'cost',
						render: function(data, type, row) {
							return data ? '₫' + parseFloat(data).toLocaleString() : '₫0';
						}
					},
					{
						data: 'conversions',
						name: 'conversions',
						render: function(data, type, row) {
							return data ? parseInt(data).toLocaleString() : '0';
						}
					},
					{
						data: null,
						orderable: false,
						render: function(data, type, row) {
							return `
								<div class="dropdown">
									<button class="btn btn-soft-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
										<i class="ri-more-fill"></i>
									</button>
									<ul class="dropdown-menu">
										<li><a class="dropdown-item" href="/dsp/googleads/campaigns/edit?id=${row.id}">
											<i class="ri-pencil-line me-2"></i>Edit
										</a></li>
										<li><a class="dropdown-item" href="#" onclick="duplicateCampaign(${row.id})">
											<i class="ri-file-copy-line me-2"></i>Duplicate
										</a></li>
										<li><hr class="dropdown-divider"></li>
										<li><a class="dropdown-item" href="#" onclick="toggleCampaignStatus(${row.id}, '${row.status}')">
											<i class="ri-play-pause-line me-2"></i>${row.status === 'ACTIVE' ? 'Pause' : 'Activate'}
										</a></li>
										<li><hr class="dropdown-divider"></li>
										<li><a class="dropdown-item text-danger" href="#" onclick="deleteCampaign(${row.id})">
											<i class="ri-delete-bin-line me-2"></i>Delete
										</a></li>
									</ul>
								</div>
							`;
						}
					}
				],
				responsive: true,
				language: {
					processing: '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>',
					emptyTable: 'No campaigns found',
					zeroRecords: 'No matching campaigns found'
				}
			});
		});

		// Campaign management functions
		function toggleCampaignStatus(campaignId, currentStatus) {
			const newStatus = currentStatus === 'ACTIVE' ? 'PAUSED' : 'ACTIVE';
			const action = newStatus === 'ACTIVE' ? 'activate' : 'pause';

			if (confirm(`Are you sure you want to ${action} this campaign?`)) {
				// TODO: Implement status update API call
				console.log(`Toggle campaign ${campaignId} from ${currentStatus} to ${newStatus}`);
			}
		}

		function duplicateCampaign(campaignId) {
			if (confirm('Are you sure you want to duplicate this campaign?')) {
				// TODO: Implement duplicate API call
				console.log(`Duplicate campaign ${campaignId}`);
			}
		}

		function deleteCampaign(campaignId) {
			if (confirm('Are you sure you want to delete this campaign? This action cannot be undone.')) {
				// TODO: Implement delete API call
				console.log(`Delete campaign ${campaignId}`);
			}
		}
	</script>
}
