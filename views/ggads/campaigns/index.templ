package campaigns

import "googledsp/views/layouts"
import "googledsp/views/ggads/campaigns/components"
import cop "googledsp/views/ggads/components"

func getDataLayoutMaster(data *ListTableCampaignLayoutData) layouts.LayoutMasterData {
	return layouts.LayoutMasterData{}
}

func getDataLayoutTable(data *ListTableCampaignLayoutData) components.LayoutTableData {
	return components.LayoutTableData{}
}

templ ListTableCampaign(data *ListTableCampaignLayoutData) {
	{{
	dataLayoutMaster := getDataLayoutMaster(data)
	// pathBreadcrumb := getPathBreadcrumb()
	// dataLayoutTable := getDataLayoutTable(data)
	}}
	@layouts.Master(&dataLayoutMaster, &[]templ.Component{cssHeader()}, scriptCampaign()) {
		// @layoutCops.ListBreadcrumdCpn("Ad Groups", pathBreadcrumb)
		if data != nil && data.FlashMsg != "" {
			@cop.FlashMsgCop(data.FlashMsg)
		}
		<div>Campaign</div>
	}
}

templ cssHeader() {
}

templ scriptCampaign() {
}
