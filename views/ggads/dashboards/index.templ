package dashboards

import "googledsp/views/layouts"
import "googledsp/views/ggads/dashboards/components"

func getDefaultDashboardData() *components.DashboardReportData {
	return &components.DashboardReportData{
		DateFilter: &components.DateFilterData{
			StartDate: "2024-01-01",
			EndDate:   "2024-01-31",
			Presets: []components.DatePreset{
				{Label: "Today", Value: "today", Days: 1},
				{Label: "Yesterday", Value: "yesterday", Days: 1},
				{Label: "Last 7 days", Value: "7d", Days: 7},
				{Label: "Last 30 days", Value: "30d", Days: 30},
				{Label: "This month", Value: "month", Days: 30},
				{Label: "Last month", Value: "last_month", Days: 30},
			},
			ShowCompare: true,
		},
		Metrics: &components.DashboardMetrics{
			Impressions: components.MetricCardData{
				Title:      "Impressions",
				Value:      "1,275,042",
				Change:     "+585,058",
				ChangeType: "positive",
				Icon:       "ri-eye-line",
				Color:      "primary",
			},
			Cost: components.MetricCardData{
				Title:      "Cost",
				Value:      "15,261,159",
				Change:     "+5,780,055",
				ChangeType: "negative",
				Icon:       "ri-money-dollar-circle-line",
				Color:      "danger",
				Prefix:     "₫",
			},
			Conversions: components.MetricCardData{
				Title:      "Conversions",
				Value:      "0.00",
				Change:     "+0.00",
				ChangeType: "neutral",
				Icon:       "ri-exchange-line",
				Color:      "warning",
			},
			Views: components.MetricCardData{
				Title:      "Views",
				Value:      "412,595",
				Change:     "+12,173",
				ChangeType: "positive",
				Icon:       "ri-bar-chart-line",
				Color:      "info",
			},
			AvgCPA: components.MetricCardData{
				Title:      "Avg. target CPA",
				Value:      "—",
				Change:     "—",
				ChangeType: "neutral",
				Icon:       "ri-target-line",
				Color:      "warning",
			},
			AvgCPM: components.MetricCardData{
				Title:      "Avg. CPM",
				Value:      "11,969",
				Change:     "+41,772",
				ChangeType: "positive",
				Icon:       "ri-funds-line",
				Color:      "success",
				Prefix:     "₫",
			},
		},
		ChartData: &components.PerformanceChartData{
			Title:       "Performance Overview",
			TimeRange:   "Last 30 days",
			ShowFilters: true,
		},
		ShowFilters: true,
		LastUpdated: "2024-01-24 14:30:00",
	}
}

templ Index() {
	@layouts.Master(nil, &[]templ.Component{dashboardHead()}, dashboardScripts()) {
		<div class="row">
			<div class="col-12">
				<div class="page-title-box d-sm-flex align-items-center justify-content-between bg-galaxy-transparent">
					<h4 class="mb-sm-0">Google Ads Dashboard</h4>
					<div class="page-title-right">
						<ol class="breadcrumb m-0">
							<li class="breadcrumb-item"><a href="javascript: void(0);">Dashboard</a></li>
							<li class="breadcrumb-item active">Overview</li>
						</ol>
					</div>
				</div>
			</div>
		</div>
		<!-- Dashboard Report Component -->
		@components.DashboardReport(getDefaultDashboardData())
	}
}

templ dashboardHead() {
	@components.DateFilterScripts()
	<script src="https://unpkg.com/htmx.org@1.9.10"></script>
	<style>
        .htmx-indicator {
            display: none;
        }
        .htmx-request .htmx-indicator {
            display: block;
        }
        .htmx-request.htmx-indicator {
            display: block;
        }
        .metrics-loading .placeholder {
            animation: placeholder-glow 2s ease-in-out infinite alternate;
        }
    </style>
}

templ dashboardScripts() {
	@components.ChartScripts()
	<script>
        // Initialize HTMX indicators
        document.addEventListener('DOMContentLoaded', function() {
            // Add loading states for HTMX requests
            document.addEventListener('htmx:beforeRequest', function(evt) {
                const target = evt.detail.target;
                if (target) {
                    target.style.opacity = '0.6';
                }
            });

            document.addEventListener('htmx:afterRequest', function(evt) {
                const target = evt.detail.target;
                if (target) {
                    target.style.opacity = '1';
                }
            });
        });
    </script>
}
