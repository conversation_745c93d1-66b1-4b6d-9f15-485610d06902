package components

type DashboardReportData struct {
	DateFilter  *DateFilterData
	Metrics     *DashboardMetrics
	ChartData   *PerformanceChartData
	ShowFilters bool
	LastUpdated string
}

templ DashboardReport(data *DashboardReportData) {
	<div id="dashboard-content">
		<!-- Date Filter -->
		@DateFilter(data.DateFilter)
		<!-- Metrics Cards -->
		<div class="row mb-4">
			<div class="col-12">
				@MetricsCards(data.Metrics)
			</div>
		</div>
		<!-- Performance Chart -->
		<div class="row mb-4">
			<div class="col-12">
				@PerformanceChart(data.ChartData)
			</div>
		</div>
		<!-- Additional Reports Row -->
		<div class="row">
			<div class="col-xl-8">
				@CampaignPerformanceTable()
			</div>
			<div class="col-xl-4">
				@TopPerformingCampaigns()
			</div>
		</div>
		<!-- Last Updated Info -->
		<div class="row mt-3">
			<div class="col-12">
				<div class="text-muted text-center">
					<small>Last updated: { data.LastUpdated }</small>
					<button
						class="btn btn-link btn-sm p-0 ms-2"
						hx-get="/api/dashboard/refresh"
						hx-target="#dashboard-content"
						hx-swap="innerHTML"
						hx-indicator="#loading-indicator"
					>
						<i class="ri-refresh-line"></i>
					</button>
				</div>
			</div>
		</div>
	</div>
	<!-- Loading Indicator -->
	<div id="loading-indicator" class="htmx-indicator">
		<div class="d-flex justify-content-center">
			<div class="spinner-border text-primary" role="status">
				<span class="visually-hidden">Loading...</span>
			</div>
		</div>
	</div>
}

templ CampaignPerformanceTable() {
	<div class="card">
		<div class="card-header d-flex align-items-center">
			<h5 class="card-title mb-0 flex-grow-1">Campaign Performance</h5>
			<div class="flex-shrink-0">
				<button
					type="button"
					class="btn btn-soft-primary btn-sm"
					hx-get="/api/dashboard/campaign-performance"
					hx-target="#campaign-table-body"
					hx-swap="innerHTML"
				>
					<i class="ri-refresh-line"></i>
				</button>
			</div>
		</div>
		<div class="card-body">
			<div class="table-responsive">
				<table class="table table-hover table-nowrap align-middle mb-0">
					<thead class="table-light">
						<tr>
							<th scope="col">Campaign</th>
							<th scope="col">Impressions</th>
							<th scope="col">Clicks</th>
							<th scope="col">CTR</th>
							<th scope="col">Cost</th>
							<th scope="col">Conversions</th>
							<th scope="col">CPA</th>
							<th scope="col">Status</th>
						</tr>
					</thead>
					<tbody id="campaign-table-body">
						@CampaignTableRows()
					</tbody>
				</table>
			</div>
		</div>
	</div>
}

templ CampaignTableRows() {
	<tr>
		<td>
			<div class="d-flex align-items-center">
				<div class="flex-shrink-0 me-2">
					<div class="avatar-xs">
						<div class="avatar-title bg-soft-success text-success rounded-circle fs-13">
							<i class="ri-advertisement-line"></i>
						</div>
					</div>
				</div>
				<div class="flex-grow-1">
					<h6 class="mb-0">Summer Sale 2024</h6>
					<p class="text-muted mb-0">ID: 12345</p>
				</div>
			</div>
		</td>
		<td>1,275,042</td>
		<td>15,261</td>
		<td>1.2%</td>
		<td>$1,159</td>
		<td>412</td>
		<td>$2.81</td>
		<td>
			<span class="badge bg-success-subtle text-success">Active</span>
		</td>
	</tr>
	<tr>
		<td>
			<div class="d-flex align-items-center">
				<div class="flex-shrink-0 me-2">
					<div class="avatar-xs">
						<div class="avatar-title bg-soft-warning text-warning rounded-circle fs-13">
							<i class="ri-advertisement-line"></i>
						</div>
					</div>
				</div>
				<div class="flex-grow-1">
					<h6 class="mb-0">Holiday Promotion</h6>
					<p class="text-muted mb-0">ID: 67890</p>
				</div>
			</div>
		</td>
		<td>892,156</td>
		<td>8,921</td>
		<td>1.0%</td>
		<td>$892</td>
		<td>178</td>
		<td>$5.01</td>
		<td>
			<span class="badge bg-warning-subtle text-warning">Paused</span>
		</td>
	</tr>
}

templ TopPerformingCampaigns() {
	<div class="card">
		<div class="card-header">
			<h5 class="card-title mb-0">Top Performing Campaigns</h5>
		</div>
		<div class="card-body">
			<div class="d-flex align-items-center mb-3">
				<div class="flex-shrink-0">
					<div class="avatar-sm">
						<div class="avatar-title bg-soft-primary text-primary rounded fs-3">
							1
						</div>
					</div>
				</div>
				<div class="flex-grow-1 ms-3">
					<h6 class="mb-1">Summer Sale 2024</h6>
					<p class="text-muted mb-0">ROAS: 4.2x</p>
				</div>
				<div class="flex-shrink-0">
					<h6 class="mb-0 text-success">+12.5%</h6>
				</div>
			</div>
			<div class="d-flex align-items-center mb-3">
				<div class="flex-shrink-0">
					<div class="avatar-sm">
						<div class="avatar-title bg-soft-success text-success rounded fs-3">
							2
						</div>
					</div>
				</div>
				<div class="flex-grow-1 ms-3">
					<h6 class="mb-1">Back to School</h6>
					<p class="text-muted mb-0">ROAS: 3.8x</p>
				</div>
				<div class="flex-shrink-0">
					<h6 class="mb-0 text-success">+8.3%</h6>
				</div>
			</div>
			<div class="d-flex align-items-center">
				<div class="flex-shrink-0">
					<div class="avatar-sm">
						<div class="avatar-title bg-soft-warning text-warning rounded fs-3">
							3
						</div>
					</div>
				</div>
				<div class="flex-grow-1 ms-3">
					<h6 class="mb-1">Holiday Promotion</h6>
					<p class="text-muted mb-0">ROAS: 3.1x</p>
				</div>
				<div class="flex-shrink-0">
					<h6 class="mb-0 text-danger">-2.1%</h6>
				</div>
			</div>
		</div>
	</div>
}
