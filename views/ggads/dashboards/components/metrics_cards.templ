package components

import "fmt"

type MetricCardData struct {
	Title      string
	Value      string
	Change     string
	ChangeType string // "positive", "negative", "neutral"
	Icon       string
	Color      string // "primary", "success", "warning", "danger", "info"
	Prefix     string
	Suffix     string
}

type DashboardMetrics struct {
	Impressions MetricCardData
	Cost        MetricCardData
	Conversions MetricCardData
	Views       MetricCardData
	AvgCPA      MetricCardData
	AvgCPM      MetricCardData
}

templ MetricsCards(metrics *DashboardMetrics) {
	<div class="row">
		<div class="col-xl-2 col-md-4">
			@MetricCard(&metrics.Impressions)
		</div>
		<div class="col-xl-2 col-md-4">
			@MetricCard(&metrics.Cost)
		</div>
		<div class="col-xl-2 col-md-4">
			@MetricCard(&metrics.Conversions)
		</div>
		<div class="col-xl-2 col-md-4">
			@MetricCard(&metrics.Views)
		</div>
		<div class="col-xl-2 col-md-4">
			@MetricCard(&metrics.AvgCPA)
		</div>
		<div class="col-xl-2 col-md-4">
			@MetricCard(&metrics.AvgCPM)
		</div>
	</div>
}

templ MetricCard(data *MetricCardData) {
	<div class="card card-animate">
		<div class="card-body">
			<div class="d-flex align-items-center">
				<div class="flex-grow-1 overflow-hidden">
					<p class="text-uppercase fw-medium text-muted text-truncate mb-0">{ data.Title }</p>
				</div>
				<div class="flex-shrink-0">
					<h5 class={ fmt.Sprintf("text-%s mb-0", data.Color) }>
						if data.ChangeType == "positive" {
							<i class="ri-arrow-up-line fs-13 align-middle"></i>
						} else if data.ChangeType == "negative" {
							<i class="ri-arrow-down-line fs-13 align-middle"></i>
						}
						{ data.Change }
					</h5>
				</div>
			</div>
			<div class="d-flex align-items-end justify-content-between mt-4">
				<div>
					<h4 class="fs-22 fw-semibold ff-secondary mb-4">
						<span class="counter-value" data-target={ data.Value }>
							{ data.Prefix }{ data.Value }{ data.Suffix }
						</span>
					</h4>
					<a href="#" class="text-decoration-underline">View net earnings</a>
				</div>
				<div class="avatar-sm flex-shrink-0">
					<span class={ fmt.Sprintf("avatar-title bg-soft-%s rounded fs-3", data.Color) }>
						<i class={ data.Icon }></i>
					</span>
				</div>
			</div>
		</div>
	</div>
}

templ MetricsCardsWithHTMX() {
	<div
		id="metrics-container"
		hx-get="/api/dashboard/metrics"
		hx-trigger="load, every 30s"
		hx-swap="innerHTML"
		class="metrics-loading"
	>
		<!-- Loading state -->
		<div class="row">
			for i := 0; i < 6; i++ {
				<div class="col-xl-2 col-md-4">
					<div class="card card-animate">
						<div class="card-body">
							<div class="placeholder-glow">
								<span class="placeholder col-6"></span>
								<span class="placeholder col-4"></span>
								<span class="placeholder col-8"></span>
							</div>
						</div>
					</div>
				</div>
			}
		</div>
	</div>
}

templ MetricsCardsResponse(metrics *DashboardMetrics) {
	@MetricsCards(metrics)
}
