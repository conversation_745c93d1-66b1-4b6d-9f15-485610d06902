package components

import "github.com/dev-networldasia/dspgos/gos/templates"

type DateFilterData struct {
	StartDate   string
	EndDate     string
	Presets     []DatePreset
	ShowCompare bool
}

type DatePreset struct {
	Label string
	Value string
	Days  int
}

templ DateFilter(data *DateFilterData) {
	<div class="row mb-3">
		<div class="col-12">
			<div class="card">
				<div class="card-body">
					<div class="row align-items-center">
						<div class="col-md-6">
							<div class="d-flex align-items-center gap-2">
								<label class="form-label mb-0">Date Range:</label>
								<div class="input-group" style="width: 300px;">
									<input 
										type="text" 
										class="form-control" 
										id="daterange-picker"
										value={ data.StartDate + " - " + data.EndDate }
										hx-get="/api/dashboard/update-date-range"
										hx-trigger="change"
										hx-target="#dashboard-content"
										hx-swap="innerHTML"
										hx-include="[name='compare']"
									/>
									<span class="input-group-text">
										<i class="ri-calendar-line"></i>
									</span>
								</div>
							</div>
						</div>
						<div class="col-md-6">
							<div class="d-flex align-items-center justify-content-md-end gap-3">
								if data.ShowCompare {
									<div class="form-check form-switch">
										<input 
											class="form-check-input" 
											type="checkbox" 
											id="compare-periods"
											name="compare"
											hx-get="/api/dashboard/toggle-compare"
											hx-trigger="change"
											hx-target="#dashboard-content"
											hx-swap="innerHTML"
											hx-include="#daterange-picker"
										/>
										<label class="form-check-label" for="compare-periods">
											Compare Periods
										</label>
									</div>
								}
								<div class="btn-group" role="group">
									for _, preset := range data.Presets {
										<button 
											type="button" 
											class="btn btn-outline-primary btn-sm"
											hx-get="/api/dashboard/set-preset"
											hx-trigger="click"
											hx-target="#dashboard-content"
											hx-swap="innerHTML"
											hx-vals={ `{"preset": "` + preset.Value + `"}` }
										>
											{ preset.Label }
										</button>
									}
								</div>
								<button 
									type="button" 
									class="btn btn-primary btn-sm"
									hx-get="/api/dashboard/refresh"
									hx-trigger="click"
									hx-target="#dashboard-content"
									hx-swap="innerHTML"
									hx-include="#daterange-picker, [name='compare']"
								>
									<i class="ri-refresh-line"></i> Refresh
								</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
}

templ DateFilterScripts() {
	<link href={ templates.AssetURL("/static/themes/libs/flatpickr/flatpickr.min.css") } rel="stylesheet" type="text/css"/>
	<script src={ templates.AssetURL("/static/themes/libs/flatpickr/flatpickr.min.js") }></script>
	<script>
		document.addEventListener('DOMContentLoaded', function() {
			initDateRangePicker();
		});

		function initDateRangePicker() {
			flatpickr("#daterange-picker", {
				mode: "range",
				dateFormat: "Y-m-d",
				defaultDate: [new Date().fp_incr(-30), new Date()],
				onChange: function(selectedDates, dateStr, instance) {
					// Trigger HTMX request when date changes
					htmx.trigger('#daterange-picker', 'change');
				}
			});
		}

		// Reinitialize date picker after HTMX swaps
		document.addEventListener('htmx:afterSwap', function(evt) {
			if (evt.detail.target.id === 'dashboard-content') {
				initDateRangePicker();
			}
		});
	</script>
}
