package components

import "github.com/dev-networldasia/dspgos/gos/templates"

type ChartData struct {
	Labels []string
	Series []ChartSeries
}

type ChartSeries struct {
	Name string
	Data []float64
	Color string
}

type PerformanceChartData struct {
	Title       string
	ChartData   *ChartData
	TimeRange   string
	ShowFilters bool
}

templ PerformanceChart(data *PerformanceChartData) {
	<div class="card">
		<div class="card-header border-0 align-items-center d-flex">
			<h4 class="card-title mb-0 flex-grow-1">{ data.Title }</h4>
			<div class="d-flex gap-1">
				if data.ShowFilters {
					<button type="button" class="btn btn-soft-secondary btn-sm">
						<i class="ri-file-list-3-line align-middle"></i> Generate Report
					</button>
					<button type="button" class="btn btn-soft-secondary btn-sm">
						<i class="ri-equalizer-line align-middle"></i>
					</button>
				}
			</div>
		</div>
		<div class="card-header p-0 border-0 bg-soft-light">
			<div class="row g-0 text-center">
				<div class="col-6 col-sm-3">
					<div class="p-3 border border-dashed border-start-0">
						<h5 class="mb-1">
							<span class="counter-value" data-target="7585">0</span>
						</h5>
						<p class="text-muted mb-0">Orders</p>
					</div>
				</div>
				<div class="col-6 col-sm-3">
					<div class="p-3 border border-dashed border-start-0">
						<h5 class="mb-1">
							$<span class="counter-value" data-target="22.89">0</span>k
						</h5>
						<p class="text-muted mb-0">Earnings</p>
					</div>
				</div>
				<div class="col-6 col-sm-3">
					<div class="p-3 border border-dashed border-start-0">
						<h5 class="mb-1">
							<span class="counter-value" data-target="367">0</span>
						</h5>
						<p class="text-muted mb-0">Refunds</p>
					</div>
				</div>
				<div class="col-6 col-sm-3">
					<div class="p-3 border border-dashed border-start-0 border-end-0">
						<h5 class="mb-1 text-success">
							<span class="counter-value" data-target="18.92">0</span>%
						</h5>
						<p class="text-muted mb-0">Conversation Ratio</p>
					</div>
				</div>
			</div>
		</div>
		<div class="card-body p-0 pb-2">
			<div class="w-100">
				<div id="customer_impression_charts" data-colors='["--vz-primary", "--vz-success", "--vz-danger"]' class="apex-charts" dir="ltr"></div>
			</div>
		</div>
	</div>
}

templ PerformanceChartWithHTMX() {
	<div 
		id="performance-chart-container"
		hx-get="/api/dashboard/performance-chart"
		hx-trigger="load"
		hx-swap="innerHTML"
	>
		<!-- Loading state -->
		<div class="card">
			<div class="card-header">
				<div class="placeholder-glow">
					<span class="placeholder col-4"></span>
				</div>
			</div>
			<div class="card-body">
				<div class="placeholder-glow">
					<span class="placeholder col-12" style="height: 300px;"></span>
				</div>
			</div>
		</div>
	</div>
}

templ ChartScripts() {
	<script src={ templates.AssetURL("/static/themes/libs/apexcharts/apexcharts.min.js") }></script>
	<script>
		function initPerformanceChart(chartData) {
			var options = {
				series: chartData.series,
				chart: {
					height: 350,
					type: 'line',
					toolbar: {
						show: false
					}
				},
				stroke: {
					width: [0, 4, 4],
					curve: 'smooth'
				},
				plotOptions: {
					bar: {
						columnWidth: '50%'
					}
				},
				colors: ['#038edc', '#51d28c', '#f34e4e'],
				dataLabels: {
					enabled: true,
					enabledOnSeries: [1, 2]
				},
				labels: chartData.labels,
				xaxis: {
					type: 'datetime'
				},
				yaxis: [{
					title: {
						text: 'Impressions',
					},
				}, {
					opposite: true,
					title: {
						text: 'Cost'
					}
				}, {
					opposite: true,
					title: {
						text: 'Conversions'
					}
				}]
			};

			var chart = new ApexCharts(document.querySelector("#customer_impression_charts"), options);
			chart.render();
		}

		// Initialize chart when data is loaded via HTMX
		document.addEventListener('htmx:afterSwap', function(evt) {
			if (evt.detail.target.id === 'performance-chart-container') {
				// Chart data would be passed from the server response
				initPerformanceChart(window.chartData || {});
			}
		});
	</script>
}
