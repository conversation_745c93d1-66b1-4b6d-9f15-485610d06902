package ads

import "googledsp/views/layouts"
import "googledsp/views/ggads/campaigns/components"
import cop "googledsp/views/ggads/components"

func getDataLayoutMaster(data *ListTableAdLayoutData) layouts.LayoutMasterData {
	return layouts.LayoutMasterData{}
}

func getDataLayoutTable(data *ListTableAdLayoutData) components.LayoutTableData {
	return components.LayoutTableData{}
}

templ ListTableAd(data *ListTableAdLayoutData) {
	{{
		dataLayoutMaster := getDataLayoutMaster(data)
		pathBreadcrumb := []cop.PathBreadcrumb{
			{Title: "Ads", DataKey: "t-ads", Url: "/dsp/googleads/ads/list"},
			{Title: "List", DataKey: "t-ads", Url: "/dsp/googleads/ads/list#"},
		}
		// dataLayoutTable := getDataLayoutTable(data)
	}}
	@layouts.Master(&dataLayoutMaster, &[]templ.Component{cssHeader()}, scriptAdgroup()) {
		@cop.ListBreadcrumdCpn("Ads", pathBreadcrumb)
		if data != nil && data.FlashMsg != "" {
			@cop.FlashMsgCop(data.FlashMsg)
		}
		<div>Ad</div>
	}
}

templ cssHeader() {
}

templ scriptAdgroup() {
}
