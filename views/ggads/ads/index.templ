package ads

import (
	"googledsp/views/ggads/campaigns/components"
	"googledsp/views/layouts"

	"github.com/dev-networldasia/dspgos/gos/templates"
	adCp "googledsp/views/ggads/ads/components"
	cop "googledsp/views/ggads/components"
)

func getDataLayoutMaster(data *ListTableAdLayoutData) layouts.LayoutMasterData {
	return layouts.LayoutMasterData{}
}

func getDataLayoutTable(data *ListTableAdLayoutData) components.LayoutTableData {
	return components.LayoutTableData{}
}

templ ListTableAd(data *ListTableAdLayoutData) {
	{{
		dataLayoutMaster := getDataLayoutMaster(data)
		pathBreadcrumb := []cop.PathBreadcrumb{
			{Title: "Ads", DataKey: "t-ads", Url: "/dsp/googleads/ads/list"},
			{Title: "List", DataKey: "t-ads", Url: "/dsp/googleads/ads/list#"},
		}
		// dataLayoutTable := getDataLayoutTable(data)
	}}
	@layouts.Master(&dataLayoutMaster, &[]templ.Component{cssHeader()}, scriptAd()) {
		@cop.ListBreadcrumdCpn("Ads", pathBreadcrumb)
		if data != nil && data.FlashMsg != "" {
			@cop.FlashMsgCop(data.FlashMsg)
		}
		<div class="row">
			<div class="col-lg-12">
				<div class="card">
					<div class="card-header d-flex justify-content-between">
						@adCp.FilterHeaderTableCp(&dataLayoutMaster)
					</div>
					<div class="card-body pt-0">
						@adCp.ListTableCp()
					</div>
				</div>
			</div>
		</div>
	}
}

templ cssHeader() {
	<link href={ templates.AssetURL("/static/themes/libs/datatables/datatables.min.css") } rel="stylesheet" type="text/css"/>
}

templ scriptAd() {
	<script type="text/javascript" src={ templates.AssetURL("/static/js/common/alert.js") }></script>
	<script type="text/javascript" src={ templates.AssetURL("/static/themes/libs/moment/moment.min.js") }></script>
	<script type="text/javascript" src={ templates.AssetURL("/static/themes/libs/daterangepicker/daterangepicker.min.js") }></script>
	<script type="text/javascript" src={ templates.AssetURL("/static/themes/libs/datatables/datatables.min.js") } defer></script>
	<script type="text/javascript" src={ templates.AssetURL("/static/themes/libs/mathjs/math.min.js") } defer></script>
}
