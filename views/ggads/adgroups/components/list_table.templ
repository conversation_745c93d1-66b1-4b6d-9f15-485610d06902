package components

import "github.com/dev-networldasia/dspgos/gos/templates"

// import "github.com/dev-networldasia/dspgos/gos/templates"
templ ListTableCp() {
	@cssListTableCp()
	<div class="table-responsive table-card mt-0">
		<table
			id="data-table-adgroup"
			class="table table-cus  table-bordered table-hover dataTable table-striped align-middle table-nowrap"
			style="width: 100%;"
			data-url={ templates.SafeURL("/dsp/googleads/api/adgroups/list-table") }
		></table>
	</div>
	@HtmlTemplateTable()
	@scriptListTableCp()
}

templ cssListTableCp() {
	<style>
      .dt-scroll-foot table{
            border-bottom: 1px solid var(--vz-border-color); 
      }
   </style>
}

templ scriptListTableCp() {
	<script type="module" defer src={ templates.AssetURL("/static/js/ggads/adgroup/list_table.js") }></script>
}
