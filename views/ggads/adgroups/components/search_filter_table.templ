package components

templ SearchFilterTableCp() {
	<div
		class="input-group input-group-sm flex-nowrap"
		style="margin-top: -5px; margin-bottom: -5px; white-space: nowrap;"
	>
		<input
			type="text"
			class="form-control"
			id="valueSearchTable"
			placeholder="Search adgroup by Name or Id"
			style="min-width: 220px;"
		/>
		<button class="btn btn-primary" type="button" id="btnSearchTable">
			Search <i class="ri-search-2-line align-middle"></i>
		</button>
	</div>
	@scriptSearchTableCp()
}

templ scriptSearchTableCp() {
	<script type="module" defer>
		function onSearchTable() {
        $("#btnSearchTable").on("click", debounce(function () {
         	removeParamURL("page");

				const searchValue = $("#valueSearchTable").val().trim().toLowerCase();
				const isNumericSearch = /^\d+$/.test(searchValue);
				const search = {}
				
				if(/^\d+$/.test(searchValue)){
					search.id = searchValue;
				} else {
					search.search = searchValue;
				}

				document.dispatchEvent(
						new CustomEvent("onUpdateFilterTable", {})
				);

        },200));
        $("#valueSearchTable").on("keydown", function (e) {
            if (e.key === "Enter" || e.keyCode === 13) {
                $("#btnSearchTable").click();
            }
        });
    }
		$(document).ready(function(){
				onSearchTable();
		})
	</script>
}
