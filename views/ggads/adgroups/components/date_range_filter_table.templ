package components

import "github.com/dev-networldasia/dspgos/gos/templates"

templ DateRangeFilterTableCp() {
	@cssDateRangeFilterTableCp()
	<div
		id="filter-search-datetime"
		class="align-items-center gap-2"
		style="min-width: fit-content; white-space: nowrap; cursor: pointer;"
		class="input-group input-group-sm"
		style="margin-top: -5px; margin-bottom: -5px;"
	>
		<i class=" ri-calendar-2-line align-middle ttext-success fs-18"></i>&nbsp <span></span> <i class="ri-arrow-down-s-fill"></i>
	</div>
	@scriptDateRangeCp()
}

templ cssDateRangeFilterTableCp() {
	<link href={ templates.AssetURL("/static/themes/libs/daterangepicker/daterangepicker.min.css") } rel="stylesheet" type="text/css"/>
}

templ scriptDateRangeCp() {
	<script type="module" defer>
		const rangesDatePicker = {
			Today: [moment(), moment()],
			Yesterday: [moment().subtract(1, "days"), moment().subtract(1, "days")],
			"Last 7 days": [moment().subtract(6, "days"), moment()],
			"Last 14 days": [moment().subtract(13, "days"), moment()],
			"Last 30 days": [moment().subtract(29, "days"), moment()],
			"This week": [moment().startOf("week"), moment()],
			"This month": [moment().startOf("month"), moment()],
			"Last month": [moment().subtract(1, "month").startOf("month"), moment().subtract(1, "month").endOf("month")],
			"All time": [moment('2000-01-01'),  moment()],
		}

		function updateDate(start, end, label) {
			$("#filter-search-datetime span").html(start.format("MMMM D, YYYY") + " - " + end.format("MMMM D, YYYY"))
			document.dispatchEvent(
					new CustomEvent("onUpdateFilterTable", {
						detail: {
							start: start.format("YYYY-MM-DDTHH:mm:ss[Z]"),
							end: end.format("YYYY-MM-DDTHH:mm:ss[Z]"),
							label: label,
						},
					})
			);
		}

		function initDatePicker() {
			updateDate(moment().subtract(360, "days"), moment().subtract(1, "days"));
			$("#filter-search-datetime").daterangepicker(
					{
						timePicker: true,
						timePicker24Hour: true,
						ranges: rangesDatePicker,
						locale: {
							direction: "ltr",
							format: "MM/DD/YYYY HH:mm",
							separator: " - ",
							applyLabel: "Apply",
							cancelLabel: "Cancel",
							fromLabel: "From",
							toLabel: "To",
							customRangeLabel: "Custom",
							daysOfWeek: ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"],
							monthNames: ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"],
							firstDay: 1,
						},
						alwaysShowCalendars: true,
						startDate: moment().subtract(360, "days").format("MM/DD/YYYY"),
						endDate: moment().subtract(1, "days").format("MM/DD/YYYY"),
					},
					updateDate
			);
		}

		initDatePicker();

	</script>
}
