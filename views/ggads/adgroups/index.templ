package adgroups

import "googledsp/views/layouts"
import "googledsp/views/ggads/campaigns/components"
import cop "googledsp/views/ggads/components"

func getDataLayoutMaster(data *ListTableAdgroupLayoutData) layouts.LayoutMasterData {
	return layouts.LayoutMasterData{}
}

func getDataLayoutTable(data *ListTableAdgroupLayoutData) components.LayoutTableData {
	return components.LayoutTableData{}
}

templ ListTableAdgroup(data *ListTableAdgroupLayoutData) {
	{{
	dataLayoutMaster := getDataLayoutMaster(data)
	// pathBreadcrumb := getPathBreadcrumb()
	// dataLayoutTable := getDataLayoutTable(data)
	}}
	@layouts.Master(&dataLayoutMaster, &[]templ.Component{cssHeader()}, scriptAdgroup()) {
		// @layoutCops.ListBreadcrumdCpn("Ad Groups", pathBreadcrumb)
		if data != nil && data.FlashMsg != "" {
			@cop.FlashMsgCop(data.FlashMsg)
		}
		<div>Adgroup</div>
	}
}

templ cssHeader() {
}

templ scriptAdgroup() {
}
