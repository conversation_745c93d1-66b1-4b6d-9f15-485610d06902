package adgroups

import "googledsp/views/layouts"
import "googledsp/views/ggads/campaigns/components"
import cop "googledsp/views/ggads/components"

func getDataLayoutMaster(data *ListTableAdgroupLayoutData) layouts.LayoutMasterData {
	return layouts.LayoutMasterData{}
}

func getDataLayoutTable(data *ListTableAdgroupLayoutData) components.LayoutTableData {
	return components.LayoutTableData{}
}

templ ListTableAdgroup(data *ListTableAdgroupLayoutData) {
	{{
	dataLayoutMaster := getDataLayoutMaster(data)
	pathBreadcrumb := []cop.PathBreadcrumb{
		{Title: "Ad Groups", DataKey: "t-ad-groups", Url: "/dsp/googleads/adgroups/list"},
		{Title: "List", DataKey: "t-ad-groups", Url: "/dsp/googleads/adgroups/list#"},
	}
	// dataLayoutTable := getDataLayoutTable(data)
	}}
	@layouts.Master(&dataLayoutMaster, &[]templ.Component{cssHeader()}, scriptAdgroup()) {
		@cop.ListBreadcrumdCpn("Ad Groups", pathBreadcrumb)
		if data != nil && data.FlashMsg != "" {
			@cop.FlashMsgCop(data.FlashMsg)
		}
		<div>Adgroup</div>
	}
}

templ cssHeader() {
}

templ scriptAdgroup() {
}
