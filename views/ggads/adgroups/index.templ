package adgroups

import (
	"googledsp/views/ggads/campaigns/components"
	"googledsp/views/layouts"

	cop "googledsp/views/ggads/components"

	"github.com/dev-networldasia/dspgos/gos/templates"
	adgroupCp "googledsp/views/ggads/adgroups/components"
)

func getDataLayoutMaster(data *ListTableAdgroupLayoutData) layouts.LayoutMasterData {
	return layouts.LayoutMasterData{}
}

func getDataLayoutTable(data *ListTableAdgroupLayoutData) components.LayoutTableData {
	return components.LayoutTableData{}
}

templ ListTableAdgroup(data *ListTableAdgroupLayoutData) {
	{{
	dataLayoutMaster := getDataLayoutMaster(data)
	pathBreadcrumb := []cop.PathBreadcrumb{
		{Title: "Ad Groups", DataKey: "t-ad-groups", Url: "/dsp/googleads/adgroups/list"},
		{Title: "List", DataKey: "t-ad-groups", Url: "/dsp/googleads/adgroups/list#"},
	}
	// dataLayoutTable := getDataLayoutTable(data)
	}}
	@layouts.Master(&dataLayoutMaster, &[]templ.Component{cssHeader()}, scriptAdgroup()) {
		@cop.ListBreadcrumdCpn("Ad Groups", pathBreadcrumb)
		if data != nil && data.FlashMsg != "" {
			@cop.FlashMsgCop(data.FlashMsg)
		}
		<div class="row">
			<div class="col-lg-12">
				<div class="card">
					<div class="card-header d-flex justify-content-between">
						@adgroupCp.FilterHeaderTableCp(&dataLayoutMaster)
					</div>
					<div class="card-body pt-0">
						@adgroupCp.ListTableCp()
					</div>
				</div>
			</div>
		</div>
	}
}

templ cssHeader() {
	<link href={ templates.AssetURL("/static/themes/libs/datatables/datatables.min.css") } rel="stylesheet" type="text/css"/>
}

templ scriptAdgroup() {
	<script type="text/javascript" src={ templates.AssetURL("/static/js/common/alert.js") }></script>
	<script type="text/javascript" src={ templates.AssetURL("/static/themes/libs/moment/moment.min.js") }></script>
	<script type="text/javascript" src={ templates.AssetURL("/static/themes/libs/daterangepicker/daterangepicker.min.js") }></script>
	<script type="text/javascript" src={ templates.AssetURL("/static/themes/libs/datatables/datatables.min.js") } defer></script>
	<script type="text/javascript" src={ templates.AssetURL("/static/themes/libs/mathjs/math.min.js") } defer></script>
}
