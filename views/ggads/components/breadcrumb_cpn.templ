package components

import "github.com/dev-networldasia/dspgos/gos/templates"

type PathBreadcrumb struct {
	Title   string
	DataKey string
	Url     string
}

templ ListBreadcrumdCpn(titlePage string, pathBreadcrumb []PathBreadcrumb) {
	<div class="row">
		<div class="col-12">
			<div class="page-title-box d-sm-flex align-items-center justify-content-between">
				<h4 data-key="t-admins" class="mb-0">{ titlePage }</h4>
				<div class="page-title-right">
					<ol class="breadcrumb m-0">
						for idex, breadcrumb := range pathBreadcrumb {
							<li class="breadcrumb-item">
								<a
									href={ templates.SafeURL(breadcrumb.Url) }
									data-key={ breadcrumb.DataKey }
									if idex == len(pathBreadcrumb)-1 {
										class="active"
									}
								>{ breadcrumb.Title }</a>
							</li>
						}
					</ol>
				</div>
			</div>
		</div>
	</div>
}
