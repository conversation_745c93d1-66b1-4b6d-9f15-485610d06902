package ggads

import "github.com/dev-networldasia/dspgos/gos/templates"

templ GGAdsMenuSideBar() {
	<li class="nav-item">
		<a class="nav-link menu-link" href={ templates.SafeURL("/") } role="button" aria-expanded="false">
			<i class="las la-tachometer-alt"></i> <span data-key="t-dashboard">Campaigns</span>
		</a>
		<div class="menu-dropdown collapse show" id="campaigns">
			<ul class="nav nav-sm flex-column">
				<li class="nav-item">
					<a href={ templates.SafeURL("/dsp/googleads/campaigns/list") } class="nav-link" data-key="t-campaigns">
						<i class=" ri-folder-chart-2-line"></i> Campaigns
					</a>
				</li>
				<li class="nav-item">
					<a href={ templates.SafeURL("/dsp/googleads/adgroups/list") } class="nav-link" data-key="t-assets">
						<i class=" ri-folders-line"></i> Ad groups
					</a>
				</li>
				<li class="nav-item">
					<a href={ templates.SafeURL("/dsp/googleads/ads/list") } class="nav-link" data-key="t-ads">
						<i class="ri-file-chart-2-line"></i> Ads
					</a>
				</li>
				<li class="nav-item">
					<a href={ templates.SafeURL("/dsp/googleads/keywords/list") } class="nav-link" data-key="t-keywords">
						<i class=" ri-file-search-line"></i> Keywords
					</a>
				</li>
			</ul>
		</div>
	</li>
}
