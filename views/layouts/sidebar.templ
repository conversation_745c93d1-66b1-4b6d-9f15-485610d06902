package layouts

import (
	"github.com/dev-networldasia/dspgos/gos/templates"
	"googledsp/views/ggads"
)

templ Sidebar() {
	<div class="app-menu navbar-menu">
		<div class="navbar-brand-box">
			<a href={ templates.SafeURL("") } class="logo logo-dark">
				<span class="logo-sm">
					<img src={ templates.AssetURL("/static/images/fav.png") } alt="" height="50"/>
				</span>
				<span class="logo-lg">
					<img src={ templates.AssetURL("/static/images/logo.png") } alt="" height="50"/>
				</span>
			</a>
			<a href={ templates.SafeURL("") } class="logo logo-light">
				<span class="logo-sm">
					<img src={ templates.AssetURL("/static/images/fav.png") } alt="" height="50"/>
				</span>
				<span class="logo-lg">
					<img src={ templates.AssetURL("/static/images/logo.png") } alt="" height="50"/>
				</span>
			</a>
			<button type="button" class="btn btn-sm p-0 fs-20 header-item float-end btn-vertical-sm-hover" id="vertical-hover">
				<i class="ri-record-circle-line"></i>
			</button>
		</div>
		<div id="scrollbar">
			<div class="container-fluid">
				<div id="two-column-menu"></div>
				<ul class="navbar-nav" id="navbar-nav">
					<li class="menu-title"><span data-key="t-platform">Platform</span></li>
					@ggads.GGAdsMenuSideBar()
				</ul>
			</div>
		</div>
		<div class="sidebar-background"></div>
	</div>
	<div class="vertical-overlay"></div>
}
