package layouts

import "googledsp/internal"
import "googledsp/views/partials"

templ Master(head *[]templ.Component, script ...templ.Component) {
	<!DOCTYPE html>
	<html lang="en">
		<head>
			<meta charset="UTF-8"/>
			<title>GoogleDSP - Campaign Management</title>
			<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
			<link rel="shortcut icon" href={ internal.AssetURL("/static/themes/images/favicon.ico") }/>
			<!-- Bootstrap CSS -->
			<link href={ internal.AssetURL("/static/themes/libs/bootstrap/css/bootstrap.min.css") } rel="stylesheet" type="text/css"/>
			<!-- Icons CSS -->
			<link href={ internal.AssetURL("/static/themes/css/icons.min.css") } rel="stylesheet" type="text/css"/>
			<!-- App CSS -->
			<link href={ internal.AssetURL("/static/themes/css/app.min.css") } rel="stylesheet" type="text/css"/>
			if head != nil {
				for _, component := range *head {
					@component
				}
			}
		</head>
		<body>
			<div id="layout-wrapper">
				@partials.Header()
				@partials.Sidebar()
				<div class="main-content">
					<div class="page-content">
						<div class="container-fluid">
							{ children... }
						</div>
					</div>
					@partials.Footer()
				</div>
			</div>
			<!-- JAVASCRIPT -->
			<script src={ internal.AssetURL("/static/themes/libs/bootstrap/js/bootstrap.bundle.min.js") }></script>
			<script src={ internal.AssetURL("/static/themes/js/app.js") }></script>
			for _, component := range script {
				@component
			}
		</body>
	</html>
}
