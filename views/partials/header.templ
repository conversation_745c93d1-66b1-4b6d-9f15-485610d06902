package partials

import "googledsp/internal"

templ Header() {
	<header id="page-topbar">
		<div class="layout-width">
			<div class="navbar-header">
				<div class="d-flex">
					<!-- LOGO -->
					<div class="navbar-brand-box horizontal-logo">
						<a href="/" class="logo logo-dark">
							<span class="logo-sm">
								<img src={ internal.AssetURL("/static/themes/images/logo-sm.png") } alt="" height="22"/>
							</span>
							<span class="logo-lg">
								<img src={ internal.AssetURL("/static/themes/images/logo-dark.png") } alt="" height="17"/>
							</span>
						</a>
					</div>
					<button type="button" class="btn btn-sm px-3 fs-16 header-item vertical-menu-btn topnav-hamburger" id="topnav-hamburger-icon">
						<span class="hamburger-icon">
							<span></span>
							<span></span>
							<span></span>
						</span>
					</button>
				</div>
				<div class="d-flex align-items-center">
					<div class="dropdown ms-sm-3 header-item topbar-user">
						<button type="button" class="btn" id="page-header-user-dropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
							<span class="d-flex align-items-center">
								<img class="rounded-circle header-profile-user" src={ internal.AssetURL("/static/themes/images/users/avatar-1.jpg") } alt="Header Avatar"/>
								<span class="text-start ms-xl-2">
									<span class="d-none d-xl-inline-block ms-1 fw-semibold user-name-text">Admin User</span>
									<span class="d-none d-xl-block ms-1 fs-12 user-name-sub-text">Administrator</span>
								</span>
							</span>
						</button>
						<div class="dropdown-menu dropdown-menu-end">
							<h6 class="dropdown-header">Welcome Admin!</h6>
							<a class="dropdown-item" href="/profile"><i class="mdi mdi-account-circle text-muted fs-16 align-middle me-1"></i> <span class="align-middle">Profile</span></a>
							<a class="dropdown-item" href="/settings"><i class="mdi mdi-cog-outline text-muted fs-16 align-middle me-1"></i> <span class="align-middle">Settings</span></a>
							<div class="dropdown-divider"></div>
							<a class="dropdown-item" href="/logout"><i class="mdi mdi-logout text-muted fs-16 align-middle me-1"></i> <span class="align-middle">Logout</span></a>
						</div>
					</div>
				</div>
			</div>
		</div>
	</header>
}
