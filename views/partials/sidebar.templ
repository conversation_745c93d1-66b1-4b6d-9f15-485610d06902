package partials

import "googledsp/internal"

templ Sidebar() {
	<div class="app-menu navbar-menu">
		<div class="navbar-brand-box">
			<a href="/" class="logo logo-dark">
				<span class="logo-sm">
					<img src={ internal.AssetURL("/static/themes/images/logo-sm.png") } alt="" height="22"/>
				</span>
				<span class="logo-lg">
					<img src={ internal.AssetURL("/static/themes/images/logo-dark.png") } alt="" height="17"/>
				</span>
			</a>
		</div>
		<div id="scrollbar">
			<div class="container-fluid">
				<ul class="navbar-nav" id="navbar-nav">
					<li class="menu-title"><span>Menu</span></li>
					<li class="nav-item">
						<a class="nav-link menu-link" href="/dashboard">
							<i class="mdi mdi-speedometer"></i> <span>Dashboard</span>
						</a>
					</li>
					<li class="nav-item">
						<a class="nav-link menu-link" href="#sidebarCampaigns" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarCampaigns">
							<i class="mdi mdi-bullhorn"></i> <span>Campaigns</span>
						</a>
						<div class="collapse menu-dropdown" id="sidebarCampaigns">
							<ul class="nav nav-sm flex-column">
								<li class="nav-item">
									<a href="/campaigns" class="nav-link">All Campaigns</a>
								</li>
								<li class="nav-item">
									<a href="/campaigns/create" class="nav-link">Create Campaign</a>
								</li>
							</ul>
						</div>
					</li>
					<li class="nav-item">
						<a class="nav-link menu-link" href="#sidebarReports" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarReports">
							<i class="mdi mdi-chart-line"></i> <span>Reports</span>
						</a>
						<div class="collapse menu-dropdown" id="sidebarReports">
							<ul class="nav nav-sm flex-column">
								<li class="nav-item">
									<a href="/reports/performance" class="nav-link">Performance</a>
								</li>
								<li class="nav-item">
									<a href="/reports/conversion" class="nav-link">Conversion</a>
								</li>
							</ul>
						</div>
					</li>
				</ul>
			</div>
		</div>
	</div>
}
