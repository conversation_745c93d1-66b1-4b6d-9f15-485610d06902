package components

templ PerformanceChart() {
    <div class="card">
        <div class="card-header align-items-center d-flex">
            <h4 class="card-title mb-0 flex-grow-1">Performance Overview</h4>
            <div class="flex-shrink-0">
                <div class="dropdown card-header-dropdown">
                    <a class="text-reset dropdown-btn" href="#" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <span class="text-muted">Last 30 Days<i class="mdi mdi-chevron-down ms-1"></i></span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end">
                        <a class="dropdown-item" href="#">Today</a>
                        <a class="dropdown-item" href="#">Yesterday</a>
                        <a class="dropdown-item" href="#">Last 7 Days</a>
                        <a class="dropdown-item" href="#">Last 30 Days</a>
                    </div>
                </div>
            </div>
        </div><!-- end card header -->
        <div class="card-body">
            <div id="performance_overview_chart" data-colors='["--vz-primary", "--vz-success", "--vz-warning", "--vz-danger"]' class="apex-charts" dir="ltr"></div>
        </div><!-- end card-body -->
    </div><!-- end card -->
}

templ TopCampaigns() {
    <div class="card card-height-100">
        <div class="card-header align-items-center d-flex">
            <h4 class="card-title mb-0 flex-grow-1">Top Campaigns</h4>
            <div class="flex-shrink-0">
                <a href="/campaigns" class="btn btn-soft-primary btn-sm">View All</a>
            </div>
        </div><!-- end card header -->
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-borderless table-centered align-middle table-nowrap mb-0">
                    <thead class="text-muted table-light">
                        <tr>
                            <th scope="col">Campaign</th>
                            <th scope="col">ROAS</th>
                            <th scope="col">Spend</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0 me-2">
                                        <div class="avatar-xs">
                                            <div class="avatar-title bg-light text-success rounded fs-3">
                                                <i class="ri-funds-fill"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-0">Summer Sale</h6>
                                    </div>
                                </div>
                            </td>
                            <td><span class="text-success">4.2x</span></td>
                            <td>$12,450</td>
                        </tr>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0 me-2">
                                        <div class="avatar-xs">
                                            <div class="avatar-title bg-light text-info rounded fs-3">
                                                <i class="ri-trophy-fill"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-0">Holiday Promo</h6>
                                    </div>
                                </div>
                            </td>
                            <td><span class="text-success">3.8x</span></td>
                            <td>$8,920</td>
                        </tr>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0 me-2">
                                        <div class="avatar-xs">
                                            <div class="avatar-title bg-light text-warning rounded fs-3">
                                                <i class="ri-book-fill"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-0">Back to School</h6>
                                    </div>
                                </div>
                            </td>
                            <td><span class="text-success">5.1x</span></td>
                            <td>$6,750</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div><!-- end card body -->
    </div><!-- end card -->
}

templ ConversionFunnel() {
    <div class="card">
        <div class="card-header">
            <h4 class="card-title mb-0">Conversion Funnel</h4>
        </div>
        <div class="card-body">
            <div id="conversion_funnel_chart" data-colors='["--vz-primary"]' class="apex-charts" dir="ltr"></div>
        </div><!-- end card-body -->
    </div><!-- end card -->
}

templ AudienceInsights() {
    <div class="card">
        <div class="card-header">
            <h4 class="card-title mb-0">Audience Insights</h4>
        </div>
        <div class="card-body">
            <div id="audience_insights_chart" data-colors='["--vz-primary", "--vz-success", "--vz-warning", "--vz-danger", "--vz-info"]' class="apex-charts" dir="ltr"></div>
        </div><!-- end card-body -->
    </div><!-- end card -->
}
