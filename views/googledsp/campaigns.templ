package googledsp

import "googledsp/views/layouts"
import "googledsp/internal"

templ Campaigns() {
	@layouts.Master(&[]templ.Component{campaignsHead()}, campaignsScripts()) {
		<!-- start page title -->
		<div class="row">
			<div class="col-12">
				<div class="page-title-box d-sm-flex align-items-center justify-content-between">
					<h4 class="mb-sm-0">Campaigns</h4>
					<div class="page-title-right">
						<ol class="breadcrumb m-0">
							<li class="breadcrumb-item"><a href="javascript: void(0);">GoogleDSP</a></li>
							<li class="breadcrumb-item active">Campaigns</li>
						</ol>
					</div>
				</div>
			</div>
		</div>
		<!-- end page title -->

		<div class="row">
			<div class="col-lg-12">
				<div class="card">
					<div class="card-header">
						<div class="d-flex align-items-center flex-wrap gap-2">
							<div class="flex-grow-1">
								<button class="btn btn-info add-btn" data-bs-toggle="modal" data-bs-target="#showModal">
									<i class="ri-add-fill me-1 align-bottom"></i> Add Campaign
								</button>
							</div>
							<div class="flex-shrink-0">
								<div class="hstack text-nowrap gap-2">
									<button class="btn btn-soft-success">Import</button>
									<button class="btn btn-soft-info">Export</button>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!--end col-->
			<div class="col-xxl-9">
				<div class="card" id="campaignsList">
					<div class="card-header">
						<div class="row g-2">
							<div class="col-md-3">
								<div class="search-box">
									<input type="text" class="form-control search" placeholder="Search campaigns...">
									<i class="ri-search-line search-icon"></i>
								</div>
							</div>
							<div class="col-md-auto ms-auto">
								<div class="d-flex align-items-center gap-2">
									<span class="text-muted">Sort by: </span>
									<select class="form-control mb-0" data-choices data-choices-search-false>
										<option value="">Sort</option>
										<option value="name">Name</option>
										<option value="status">Status</option>
										<option value="budget">Budget</option>
										<option value="created_date">Date Created</option>
									</select>
								</div>
							</div>
						</div>
					</div>
					<div class="card-body">
						<div>
							<div class="table-responsive table-card mb-3">
								<table class="table align-middle table-nowrap mb-0" id="campaignTable">
									<thead class="table-light">
										<tr>
											<th scope="col" style="width: 50px;">
												<div class="form-check">
													<input class="form-check-input" type="checkbox" id="checkAll" value="option">
												</div>
											</th>
											<th class="sort" data-sort="campaign_name">Campaign Name</th>
											<th class="sort" data-sort="status">Status</th>
											<th class="sort" data-sort="budget">Budget</th>
											<th class="sort" data-sort="spend">Spend</th>
											<th class="sort" data-sort="roas">ROAS</th>
											<th class="sort" data-sort="conversions">Conversions</th>
											<th class="sort" data-sort="date">Date Created</th>
											<th>Action</th>
										</tr>
									</thead>
									<tbody class="list form-check-all" id="campaigns-list">
										<!-- Campaign data will be loaded here via HTMX -->
									</tbody>
								</table>
								<div class="noresult" style="display: none">
									<div class="text-center">
										<h5 class="mt-2">Sorry! No Result Found</h5>
										<p class="text-muted mb-0">We've searched more than 150+ campaigns We did not find any campaigns for you search.</p>
									</div>
								</div>
							</div>
							<div class="d-flex justify-content-end mt-3">
								<div class="pagination-wrap hstack gap-2">
									<a class="page-item pagination-prev disabled" href="#">
										Previous
									</a>
									<ul class="pagination listjs-pagination mb-0"></ul>
									<a class="page-item pagination-next" href="#">
										Next
									</a>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!--end card-->
			</div>
			<!--end col-->
			<div class="col-xxl-3">
				<div class="card" id="campaign-overview">
					<div class="card-header">
						<h5 class="card-title mb-0">Campaign Overview</h5>
					</div>
					<div class="card-body">
						<div id="campaign-overview-chart" data-colors='["--vz-primary", "--vz-info", "--vz-warning", "--vz-success"]' class="apex-charts" dir="ltr"></div>
					</div>
				</div>
			</div>
			<!--end col-->
		</div>
		<!--end row-->
	}
}

templ campaignsHead() {
	<link rel="stylesheet" href={ internal.AssetURL("/static/themes/libs/apexcharts/apexcharts.css") }/>
	<link href={ internal.AssetURL("/static/themes/libs/sweetalert2/sweetalert2.min.css") } rel="stylesheet" type="text/css"/>
}

templ campaignsScripts() {
	<script src="https://cdn.jsdelivr.net/npm/htmx.org@2.0.6/dist/htmx.min.js"></script>
	<script src={ internal.AssetURL("/static/themes/libs/apexcharts/apexcharts.min.js") }></script>
	<script src={ internal.AssetURL("/static/themes/libs/sweetalert2/sweetalert2.min.js") }></script>
	<script src={ internal.AssetURL("/static/js/pages/campaigns.js") }></script>
}
