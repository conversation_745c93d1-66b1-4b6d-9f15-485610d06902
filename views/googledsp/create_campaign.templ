package googledsp

import "googledsp/views/layouts"
import "googledsp/internal"

templ CreateCampaign() {
	@layouts.Master(&[]templ.Component{createCampaignHead()}, createCampaignScripts()) {
		<!-- start page title -->
		<div class="row">
			<div class="col-12">
				<div class="page-title-box d-sm-flex align-items-center justify-content-between">
					<h4 class="mb-sm-0">Create Campaign</h4>
					<div class="page-title-right">
						<ol class="breadcrumb m-0">
							<li class="breadcrumb-item"><a href="javascript: void(0);">GoogleDSP</a></li>
							<li class="breadcrumb-item"><a href="/campaigns">Campaigns</a></li>
							<li class="breadcrumb-item active">Create Campaign</li>
						</ol>
					</div>
				</div>
			</div>
		</div>
		<!-- end page title -->

		<form id="createCampaignForm" class="needs-validation" novalidate>
			<div class="row">
				<div class="col-lg-8">
					<div class="card">
						<div class="card-header">
							<h5 class="card-title mb-0">Campaign Details</h5>
						</div>
						<div class="card-body">
							<div class="row">
								<div class="col-lg-6">
									<div class="mb-3">
										<label for="campaignName" class="form-label">Campaign Name <span class="text-danger">*</span></label>
										<input type="text" class="form-control" id="campaignName" name="name" placeholder="Enter campaign name" required>
										<div class="invalid-feedback">
											Please provide a campaign name.
										</div>
									</div>
								</div>
								<div class="col-lg-6">
									<div class="mb-3">
										<label for="campaignStatus" class="form-label">Status</label>
										<select class="form-select" id="campaignStatus" name="status">
											<option value="DRAFT">Draft</option>
											<option value="ACTIVE">Active</option>
											<option value="PAUSED">Paused</option>
										</select>
									</div>
								</div>
							</div>

							<div class="row">
								<div class="col-lg-6">
									<div class="mb-3">
										<label for="budgetAmount" class="form-label">Daily Budget ($) <span class="text-danger">*</span></label>
										<input type="number" class="form-control" id="budgetAmount" name="budget_amount" placeholder="0.00" step="0.01" min="1" required>
										<div class="invalid-feedback">
											Please provide a valid budget amount.
										</div>
									</div>
								</div>
								<div class="col-lg-6">
									<div class="mb-3">
										<label for="bidStrategy" class="form-label">Bid Strategy</label>
										<select class="form-select" id="bidStrategy" name="bid_strategy">
											<option value="TARGET_CPA">Target CPA</option>
											<option value="TARGET_ROAS">Target ROAS</option>
											<option value="MAXIMIZE_CLICKS">Maximize Clicks</option>
											<option value="MANUAL_CPC">Manual CPC</option>
										</select>
									</div>
								</div>
							</div>

							<div class="row">
								<div class="col-lg-6">
									<div class="mb-3">
										<label for="startDate" class="form-label">Start Date <span class="text-danger">*</span></label>
										<input type="date" class="form-control" id="startDate" name="start_date" required>
										<div class="invalid-feedback">
											Please provide a start date.
										</div>
									</div>
								</div>
								<div class="col-lg-6">
									<div class="mb-3">
										<label for="endDate" class="form-label">End Date (Optional)</label>
										<input type="date" class="form-control" id="endDate" name="end_date">
									</div>
								</div>
							</div>

							<div class="mb-3">
								<label for="googleCustomerID" class="form-label">Google Customer ID <span class="text-danger">*</span></label>
								<input type="text" class="form-control" id="googleCustomerID" name="google_customer_id" placeholder="Enter Google Ads Customer ID" required>
								<div class="invalid-feedback">
									Please provide a Google Customer ID.
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="col-lg-4">
					<div class="card">
						<div class="card-header">
							<h5 class="card-title mb-0">Campaign Summary</h5>
						</div>
						<div class="card-body">
							<div class="table-responsive">
								<table class="table table-borderless mb-0">
									<tbody>
										<tr>
											<td class="text-muted">Campaign Name:</td>
											<td class="fw-medium" id="summaryName">-</td>
										</tr>
										<tr>
											<td class="text-muted">Status:</td>
											<td><span class="badge bg-secondary" id="summaryStatus">Draft</span></td>
										</tr>
										<tr>
											<td class="text-muted">Daily Budget:</td>
											<td class="fw-medium" id="summaryBudget">$0.00</td>
										</tr>
										<tr>
											<td class="text-muted">Bid Strategy:</td>
											<td class="fw-medium" id="summaryBidStrategy">Target CPA</td>
										</tr>
										<tr>
											<td class="text-muted">Start Date:</td>
											<td class="fw-medium" id="summaryStartDate">-</td>
										</tr>
										<tr>
											<td class="text-muted">End Date:</td>
											<td class="fw-medium" id="summaryEndDate">No end date</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
					</div>

					<div class="card">
						<div class="card-header">
							<h5 class="card-title mb-0">Actions</h5>
						</div>
						<div class="card-body">
							<div class="hstack gap-2">
								<button type="submit" class="btn btn-success w-100">
									<i class="ri-save-line align-bottom me-1"></i> Create Campaign
								</button>
							</div>
							<div class="hstack gap-2 mt-2">
								<button type="button" class="btn btn-soft-info w-50">
									<i class="ri-draft-line align-bottom me-1"></i> Save as Draft
								</button>
								<a href="/campaigns" class="btn btn-soft-danger w-50">
									<i class="ri-close-line align-bottom me-1"></i> Cancel
								</a>
							</div>
						</div>
					</div>
				</div>
			</div>
		</form>
	}
}

templ createCampaignHead() {
	<link href={ internal.AssetURL("/static/themes/libs/sweetalert2/sweetalert2.min.css") } rel="stylesheet" type="text/css"/>
}

templ createCampaignScripts() {
	<script src="https://cdn.jsdelivr.net/npm/htmx.org@2.0.6/dist/htmx.min.js"></script>
	<script src={ internal.AssetURL("/static/themes/libs/sweetalert2/sweetalert2.min.js") }></script>
	<script src={ internal.AssetURL("/static/js/pages/create-campaign.js") }></script>
}
