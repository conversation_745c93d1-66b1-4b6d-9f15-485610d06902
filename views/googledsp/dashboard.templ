package googledsp

import "googledsp/views/layouts"
import "googledsp/internal"

templ Dashboard() {
	@layouts.Master(&[]templ.Component{dashboardHead()}, dashboardScripts()) {
		<div class="row">
			<div class="col-12">
				<div class="page-title-box d-sm-flex align-items-center justify-content-between">
					<h4 class="mb-sm-0">Dashboard</h4>
					<div class="page-title-right">
						<ol class="breadcrumb m-0">
							<li class="breadcrumb-item"><a href="javascript: void(0);">GoogleDSP</a></li>
							<li class="breadcrumb-item active">Dashboard</li>
						</ol>
					</div>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col">
				<div class="h-100">
					<h1>Dashboard Content</h1>
					<p>Welcome to GoogleDSP Dashboard</p>
				</div>
			</div>
		</div>
	}
}

templ dashboardHead() {
	<link rel="stylesheet" href={ internal.AssetURL("/static/themes/libs/apexcharts/apexcharts.css") }/>
}

templ dashboardScripts() {
	<script src={ internal.AssetURL("/static/themes/libs/apexcharts/apexcharts.min.js") }></script>
}
