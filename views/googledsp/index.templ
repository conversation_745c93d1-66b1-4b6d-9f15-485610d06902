package googledsp

import "googledsp/views/layouts"
import "googledsp/internal"

templ Index() {
	@layouts.Master(&[]templ.Component{indexHead()}, indexScripts()) {
		<!-- start page title -->
		<div class="row">
			<div class="col-12">
				<div class="page-title-box d-sm-flex align-items-center justify-content-between">
					<h4 class="mb-sm-0">GoogleDSP Dashboard</h4>
					<div class="page-title-right">
						<ol class="breadcrumb m-0">
							<li class="breadcrumb-item"><a href="javascript: void(0);">GoogleDSP</a></li>
							<li class="breadcrumb-item active">Dashboard</li>
						</ol>
					</div>
				</div>
			</div>
		</div>
		<!-- end page title -->
		<div class="row">
			<div class="col">
				<div class="h-100">
					<div class="row">
						<div class="col-xl-3 col-md-6">
							<div class="card card-animate">
								<div class="card-body">
									<div class="d-flex align-items-center">
										<div class="flex-grow-1 overflow-hidden">
											<p class="text-uppercase fw-medium text-muted text-truncate mb-0">Total Campaigns</p>
										</div>
									</div>
									<div class="d-flex align-items-end justify-content-between mt-4">
										<div>
											<h4 class="fs-22 fw-semibold ff-secondary mb-4"><span class="counter-value" data-target="245">0</span></h4>
											<a href="/campaigns" class="text-decoration-underline">View all campaigns</a>
										</div>
										<div class="avatar-sm flex-shrink-0">
											<span class="avatar-title bg-success-subtle rounded fs-3">
												<i class="bx bx-bullhorn text-success"></i>
											</span>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	}
}

templ indexHead() {
	<link rel="stylesheet" href={ internal.AssetURL("/static/themes/libs/apexcharts/apexcharts.css") }/>
}

templ indexScripts() {
	<script src="https://cdn.jsdelivr.net/npm/htmx.org@2.0.6/dist/htmx.min.js"></script>
	<script src={ internal.AssetURL("/static/themes/libs/apexcharts/apexcharts.min.js") }></script>
	<script src={ internal.AssetURL("/static/js/pages/dashboard.js") }></script>
}
